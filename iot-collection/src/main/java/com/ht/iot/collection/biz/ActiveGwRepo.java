package com.ht.iot.collection.biz;

import com.cdz360.base.utils.StringUtils;
import com.cdz360.iot.common.utils.CollectionUtils;
import com.cdz360.iot.model.modbus.type.Rs485Protocol;
import com.ht.iot.collection.model.ActiveGw;
import com.ht.iot.collection.model.LaunchedDevice;
import com.ht.iot.collection.model.LaunchedDlt645Device;
import com.ht.iot.collection.model.LaunchedModbusRtuDevice;
import com.ht.iot.collection.model.LaunchedModbusTcpDevice;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentLinkedDeque;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class ActiveGwRepo {

    private Map<String, ActiveGw> gws = new ConcurrentHashMap<>();

    public ActiveGw getGwInfo(String gwno) {
        return this.gws.get(gwno);
    }

    public synchronized void removeGw(String gwno) {
        this.gws.remove(gwno);
    }

    /**
     * 检查设备是否有待发送的消息,如果没有则从网关移除,网关如果没有要发送的设备,移除网关
     *
     * @param gwno   网关编号
     * @param device 设备
     * @return true 网关还有要发送的采集指令
     */
    public synchronized boolean checkAndRemoveDevice(String gwno, LaunchedDevice device) {
        ActiveGw gw = gws.get(gwno);
        if (gw == null) {
            log.warn("[{} {}] 网关未在缓存中.....", gwno, device.getDno());
            return false;
        }
        boolean isEmpty = false;
        if (device instanceof LaunchedModbusRtuDevice) {
            LaunchedModbusRtuDevice modbusDevice = (LaunchedModbusRtuDevice) device;
            isEmpty = CollectionUtils.isEmpty(modbusDevice.getReadQ());
        } else if (device instanceof LaunchedDlt645Device) {
            LaunchedDlt645Device dlt645Device = (LaunchedDlt645Device) device;
            isEmpty = CollectionUtils.isEmpty(dlt645Device.getReadQ());
        }
        if (isEmpty) {
            gw.getDevices()
                .removeIf(o -> StringUtils.equalsIgnoreCase(device.getDno(), o.getDno()));
        }
        isEmpty = CollectionUtils.isEmpty(gw.getDevices());
        if (isEmpty) {
            log.info("[{} {}] 网关的采集指令下发队列为空,将网关从采集队列移除",
                gwno, device.getDno());
            this.removeGw(gwno);
        }
        return !isEmpty;
    }

    public synchronized LaunchedDevice getNextReq(String gwno) {
        ActiveGw gw = gws.get(gwno);
        if (gw == null) {
            log.error("[{}] 网关信息不存在", gwno);
            return null;
        }
        LaunchedDevice device = gw.getDevices().peek();
        if (device == null) {
            log.error("[{}] 网关需要下发的设备为空", gwno);
            return null;
        }
//        if (device instanceof LaunchedModbusDevice) {
//            device = toNextModbusReq(gw, (LaunchedModbusDevice) device);
//        } else if (device instanceof LaunchedDlt645Device) {
//            device = toNextDlt645Req(gw, (LaunchedDlt645Device) device);
//        } else {
//            log.error("[{} {}] 不识别的设备协议. device= {}", gwno, device.getDno(), device);
//            device = null;
//        }
//        if (device != null) {
//            // 设置超时时间
//            Long expireDur = device.getExpireDur() == null ? 5 : device.getExpireDur();// 默认超时时间5秒
//            LocalDateTime expireTime = LocalDateTime.now().plusSeconds(expireDur);
//            gw.setExpireTime(expireTime);
//        }
        return device;
    }

//    private LaunchedModbusDevice toNextModbusReq(ActiveGw gw, LaunchedModbusDevice device) {
//        ModbusAddrRange addr = device.getReadQ().peek();    // 下一个要读取的地址
////        if (addr == null) {
////            log.debug("[{}] 设备待发送的指令已发送完毕", device.getDno());
////            gw.getDevices().peek();
////            return null;
////        }
//        device.setReqAddr(addr);
//        return device;
//    }

//    private LaunchedDlt645Device toNextDlt645Req(ActiveGw gw, LaunchedDlt645Device device) {
//        P645DataType addr = device.getReadQ().peek();    // 下一个要读取的地址
////        if (addr == null) {
////            log.debug("[{}] 设备待发送的指令已发送完毕", device.getDno());
////            gw.getDevices().peek();
////            return null;
////        }
//
//        device.setReqAddr(addr);
//        device.setCurMsgType()
//        return device;
//    }


    /**
     * @return 返回超时的网关编号
     */
    public synchronized List<String> collectTimeoutGws() {
        LocalDateTime curTime = LocalDateTime.now();
        return this.gws.values().stream()
            .filter(o -> o.getExpireTime() != null && curTime.isAfter(o.getExpireTime()))
            .map(o -> o.getGwno())
            .collect(Collectors.toList());
    }

    /**
     * @return true, 需要立即下发指令; false,不需要立即下发
     */
//    public boolean addDevice(LaunchedDevice device) {
//        if (Rs485Protocol.MODBUS == device.getProtocol()) {
//            return addModbusRtuDevice((LaunchedModbusRtuDevice) device);
//        } else if (Rs485Protocol.MODBUS_TCP == device.getProtocol()) {
//            return addModbusTcpDevice((LaunchedModbusTcpDevice) device);
//        } else if (Rs485Protocol.DLT645 == device.getProtocol()) {
//            return addDlt645Device((LaunchedDlt645Device) device);
//        } else {
//            log.error("[{} {}] 设备通信协议类型暂不支持. device= {}", device.getGwno(),
//                device.getDno(), device);
//            return false;
//        }
//    }

    /**
     * @return true, 需要立即下发指令; false,不需要立即下发
     */
//    private boolean addModbusRtuDevice(LaunchedModbusRtuDevice device) {
//        boolean launchNow = this.addDeviceX(device);    // true:立即下发
//        return launchNow;
//    }
//
//    /**
//     * @return true, 需要立即下发指令; false,不需要立即下发
//     */
//    private boolean addModbusTcpDevice(LaunchedModbusTcpDevice device) {
//        boolean launchNow = this.addDeviceX(device);    // true:立即下发
//        return launchNow;
//    }
//
//    /**
//     * @return true, 需要立即下发指令; false,不需要立即下发
//     */
//    private boolean addDlt645Device(LaunchedDlt645Device device) {
//        boolean launchNow = this.addDeviceX(device);    // true:立即下发
//        return launchNow;
//    }

    /**
     * @return true, 需要立即下发指令; false,不需要立即下发
     */
    public synchronized boolean addDevice(LaunchedDevice device) {
        ActiveGw oldGw = this.gws.get(device.getGwno());
        if (oldGw == null) {    // 网关未在队列中，添加网关和设备
            this.gws.put(device.getGwno(), this.createActiveGw(device));
            return true;
        } else if (CollectionUtils.isEmpty(oldGw.getDevices())) {   // 网关已在队列，但设备队列为空添加设备
            oldGw.getDevices().add(device);
            log.info("[{}] 网关的下发队列长度（devices.size) = {}", oldGw.getGwno(),
                oldGw.getDevices().size());
            return true;
        } else {
            Optional<LaunchedDevice> oldDeviceOpt = oldGw.getDevices().stream()
                .filter(od -> StringUtils.equals(od.getDno(), device.getDno()))
                .findFirst();
            if (oldDeviceOpt.isEmpty()) {    // 网关已在队列，但设备未在网关的队列里,将设备加如到网关的队列末尾
                oldGw.getDevices().add(device);
            } else { // 设备已在网关的队列中
                LaunchedDevice oldDevice = oldDeviceOpt.get();
                if (Rs485Protocol.MODBUS == oldDevice.getProtocol()) {
                    this.addModbusRtuDeviceX(oldGw, (LaunchedModbusRtuDevice) oldDevice,
                        (LaunchedModbusRtuDevice) device);
                } else if (Rs485Protocol.MODBUS_TCP == oldDevice.getProtocol()
                    || Rs485Protocol.MODBUS_RTU_TCP == oldDevice.getProtocol()) {
                    this.addModbusTcpDeviceX(oldGw, (LaunchedModbusTcpDevice) oldDevice,
                        (LaunchedModbusTcpDevice) device);
                } else if (Rs485Protocol.DLT645 == oldDevice.getProtocol()) {
                    this.addDlt645DeviceX(oldGw, (LaunchedDlt645Device) oldDevice,
                        (LaunchedDlt645Device) device);
                } else {
                    log.error("[{} {}] 设备通信协议类型错误, oldDevice= {}",
                        oldGw.getGwno(), oldDevice.getDno(), oldDevice);
                }
            }
            return false;
        }
//        else if (StringUtils.equals(oldGw.getDevices().peek().getDno(), device.getDno())) {
//            Iterator<LaunchedDevice> iter = oldGw.getDevices().iterator();
//            iter.next();  // 跳过第一个
//            while (iter.hasNext()) { // 移除队列里除第一个外，其他未下发的同dno设备
//                LaunchedDevice d = iter.next();
//                if (StringUtils.equals(d.getDno(), device.getDno())) {
//                    iter.remove();
//                }
//            }
//            // 队列里第一个就是当前要修改的设备, 暂时先让要下发的指令发送完
////            oldGw.getDevices().peek()
//            oldGw.getDevices().add(device); // 暂时先放到队列的最后
//            log.info("[{}] 网关的下发队列长度（devices.size) = {}", oldGw.getGwno(),
//                oldGw.getDevices().size());
//            return false;
//        } else {
//            oldGw.getDevices().removeIf(o -> StringUtils.equals(o.getDno(), device.getDno()));
//            oldGw.getDevices().add(device);
//            log.info("[{}] 网关的下发队列长度（devices.size) = {}", oldGw.getGwno(),
//                oldGw.getDevices().size());
//            return false;
//        }
    }

    private void addModbusRtuDeviceX(ActiveGw gw, LaunchedModbusRtuDevice oldDevice,
        LaunchedModbusRtuDevice newDevice) {
        log.info(
            "[{} {}] 设备增加要下发的ModbusRtu指令. 增加前设备待下发的队列长度= {}, 要新增的指令队列长度= {}",
            gw.getGwno(), oldDevice.getDno(), oldDevice.getQueueSize(), newDevice.getQueueSize());

        // 临时增加队列大小限制
        if (oldDevice.getQueueSize() > 28) {
            log.warn("[{} {}] Modbus设备下发指令队列超限. queueSize= {}",
                gw.getGwno(), oldDevice.getDno(), oldDevice.getQueueSize());
            return;
        }

        if (CollectionUtils.isNotEmpty(newDevice.getReadQ())) {
            oldDevice.getReadQ().addAll(newDevice.getReadQ());
        }

    }

    private void addModbusTcpDeviceX(ActiveGw gw, LaunchedModbusTcpDevice oldDevice,
        LaunchedModbusTcpDevice newDevice) {
        log.info(
            "[{} {}] 设备增加要下发的ModbusTcp指令. 增加前设备待下发的队列长度= {}, 要新增的指令队列长度= {}",
            gw.getGwno(), oldDevice.getDno(), oldDevice.getQueueSize(), newDevice.getQueueSize());

        // 临时增加队列大小限制
        if (oldDevice.getQueueSize() > 28) {
            log.warn("[{} {}] Modbus设备下发指令队列超限. queueSize= {}",
                gw.getGwno(), oldDevice.getDno(), oldDevice.getQueueSize());
            return;
        }

        if (CollectionUtils.isNotEmpty(newDevice.getReadQ())) {
            oldDevice.getReadQ().addAll(newDevice.getReadQ());
        }
    }

    private void addDlt645DeviceX(ActiveGw gw, LaunchedDlt645Device oldDevice,
        LaunchedDlt645Device newDevice) {
        log.info(
            "[{} {}] 设备增加要下发的DLT645指令. 增加前设备待下发的队列长度= {}, 要新增的指令队列长度= {}",
            gw.getGwno(), oldDevice.getDno(), oldDevice.getQueueSize(), newDevice.getQueueSize());
        if (CollectionUtils.isNotEmpty(newDevice.getWriteQ())) {
            oldDevice.getWriteQ().addAll(newDevice.getWriteQ());
        }
        if (CollectionUtils.isNotEmpty(newDevice.getReadQ())) {
            oldDevice.getReadQ().addAll(newDevice.getReadQ());
        }
        if (oldDevice.getQueueSize() > 1000) {
            log.warn("[{} {}] DLT645设备下发指令队列超限. queueSize= {}",
                gw.getGwno(), oldDevice.getDno(), oldDevice.getQueueSize());
        }
    }

    private ActiveGw createActiveGw(LaunchedDevice device) {
        ActiveGw gw = new ActiveGw();
        gw.setGwno(device.getGwno())
            .setDevices(new ConcurrentLinkedDeque<>());
        gw.getDevices().offer(device);
        return gw;
    }
}
