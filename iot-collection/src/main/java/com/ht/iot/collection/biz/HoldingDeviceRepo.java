package com.ht.iot.collection.biz;

import com.cdz360.base.model.base.exception.DcArgumentException;
import com.ht.iot.collection.model.*;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.ht.iot.collection.ds.mapper.IotDeviceMapper;
import com.ht.iot.collection.model.dto.UpdateDeviceInfoDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class HoldingDeviceRepo {

    @Autowired
    IotDeviceMapper iotDeviceMapper;

    private Map<String, HoldingDevice> devices = null;

    public synchronized void updateDevice(HoldingDevice device) {
        if (Boolean.FALSE.equals(device.getEnable())) {
            this.removeDevice(device.getDno());
        } else {
            this.devices.put(device.getDno(), device);
            this.addDevice(device);
        }
    }

    public void updateDeviceBasicInfo(UpdateDeviceInfoDto device) {
        HoldingDevice oldDevice = devices.get(device.getDno());
        if (oldDevice == null) {
            throw new DcArgumentException("无对应设备");
        }
        oldDevice.setGwno(device.getGwno());
        oldDevice.setProtocol(device.getProtocol());
        oldDevice.setDeviceType(device.getDeviceType());
        oldDevice.setCombineNotify(device.getCombineNotify());
        oldDevice.setMqttDownTopic(device.getMqttDownTopic());
        oldDevice.setNotifyCfg(device.getNotifyCfg());
        devices.put(oldDevice.getDno(), oldDevice);
        iotDeviceMapper.updateDevice(oldDevice);
    }


    private void insertDeviceByType(HoldingDevice device) {
        if (device instanceof HoldingModbusRtuDevice modbusDevice) {
            iotDeviceMapper.insertDevice(modbusDevice);
        } else if (device instanceof HoldingDlt645Device dlt645Device) {
            iotDeviceMapper.insertDevice(dlt645Device);
        } else {
            log.error("[{} {}] 不识别的设备协议. device= {}", device.getGwno(), device.getDno(), device);
        }
    }

    private void updateDbDeviceByType(HoldingDevice device) {
        if (device instanceof HoldingModbusRtuDevice modbusDevice) {
            iotDeviceMapper.updateDevice(modbusDevice);
        } else if (device instanceof HoldingDlt645Device dlt645Device) {
            iotDeviceMapper.updateDevice(dlt645Device);
        } else {
            log.error("[{} {}] 不识别的设备协议. device= {}", device.getGwno(), device.getDno(), device);
        }
    }

    private void addDevice(HoldingDevice device) {
        device.setNextTime(LocalDateTime.now());    // 新加入的设备设置为待发送
        // 设备配置落库，如库中存在相同dno数据，则更新
        if (iotDeviceMapper.getIdByDno(device.getDno()) != null) {
            this.updateDbDeviceByType(device);
        } else {
            this.insertDeviceByType(device);
        }
    }

    public void removeDevice(String dno) {
        log.info("[{}] 设置采集停止", dno);
        this.devices.remove(dno);
        iotDeviceMapper.deleteDeviceByDno(dno);
    }

    /**
     * 每10w条数据一次查询
     */
    public void loadAllDevices() {
        List<HoldingDevice> deviceList = new ArrayList<>();
        long lastId = 0;
        do {
            List<HoldingDevice> batch = iotDeviceMapper.getAllDevicesByPage(lastId);
            if (batch.isEmpty()) break;
            deviceList.addAll(batch);
            lastId = batch.get(batch.size() - 1).getId();
        } while (true);

        devices = deviceList.stream().map(i -> i.setNextTime(LocalDateTime.now())).collect(Collectors.toMap(HoldingDevice::getDno, device -> device));
    }

    /**
     * 返回需要下发采集指令的设备列表
     *
     * @return
     */
    public synchronized List<LaunchedDevice> collectLaunchingDevices() {
        LocalDateTime curTime = LocalDateTime.now();
        List<LaunchedDevice> launchingDeviceList = new ArrayList<>();

        if (devices == null) {
            loadAllDevices();
        }

        for (HoldingDevice device : devices.values()) {
            if (curTime.isAfter(device.getNextTime())) {
                device.setNextTime(curTime.plusSeconds(device.getDuration()));
                LaunchedDevice launchingDevice = DeviceConvert.toLaunchedDevice(device);
                if (launchingDevice != null) {
                    launchingDeviceList.add(launchingDevice);
                }
            }
        }
        return launchingDeviceList;
    }


}
