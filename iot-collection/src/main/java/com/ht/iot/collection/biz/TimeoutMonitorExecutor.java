package com.ht.iot.collection.biz;

import com.cdz360.iot.common.utils.CollectionUtils;
import com.cdz360.iot.model.modbus.type.Rs485Protocol;
import com.ht.iot.collection.model.ActiveGw;
import com.ht.iot.collection.model.IotCollectionConstants;
import com.ht.iot.collection.model.LaunchedDevice;
import com.ht.iot.collection.model.LaunchedDlt645Device;
import com.ht.iot.collection.model.LaunchedModbusRtuDevice;
import com.ht.iot.collection.model.LaunchedModbusTcpDevice;
import com.ht.iot.collection.model.type.SouthMsgType;
import java.time.LocalDateTime;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class TimeoutMonitorExecutor {

    @Autowired
    private LaunchingExecutor launchingExecutor;

    @Autowired
    private ActiveGwRepo gwRepo;


    public void checkTimeout() {
        List<String> timeoutGws = this.gwRepo.collectTimeoutGws();
        if (CollectionUtils.isNotEmpty(timeoutGws)) {
            log.info("超时网关数量: {}", timeoutGws.size());
            timeoutGws.stream().forEach(this::timeout);
        }
    }


    private void timeout(String timeoutGwno) {
        ActiveGw gw = gwRepo.getGwInfo(timeoutGwno);
        if (gw == null) {
            log.warn("超时网关未在缓存中. gwno= {}", timeoutGwno);
            return;
        }
        gw.setExpireTime(null); // 将超时时间置空

        LaunchedDevice device = gw.getDevices().peek(); // 去除已下发采集指令的设备

        if (device != null) {
            log.warn("[{} {}] 设备超时. protocol= {}",
                device.getGwno(), device.getDno(),
                device.getProtocol());
            Boolean isEnd = null;
            if (Rs485Protocol.MODBUS == device.getProtocol()) {
                // true表示设备已经无待发送的指令,可从采集队列移除; false表示还有待发送的消息，需要发下一个；
                isEnd = this.handleModbusDeviceTimeout(gw,
                    (LaunchedModbusRtuDevice) device);
            } else if (IotCollectionConstants.TCP_PROTOCOLS.contains(device.getProtocol())) {
                isEnd = this.handleModbusTcpDeviceTimeout(gw,
                    (LaunchedModbusTcpDevice) device);
            } else if (Rs485Protocol.DLT645 == device.getProtocol()) {
                // true表示设备已经无待发送的指令,可从采集队列移除; false表示还有待发送的消息，需要发下一个；
                isEnd = this.handleDlt645DeviceTimeout(gw, (LaunchedDlt645Device) device);
            } else {
                log.warn("[{} {}] 设备协议异常. protocol= {}",
                    device.getGwno(), device.getDno(), device.getProtocol());
            }
            if (isEnd != null) {
                gw.getDevices().poll(); // 移除第一个设备,即将超时设备从队列头上拿掉
                if (Boolean.FALSE.equals(isEnd)) {
                    gw.getDevices().add(device);    // 将设备插入到队列的末尾，等其他设备采集后再继续采集后续的内容
                    log.warn(
                        "[{} {}] 将超时设备挪到队列末尾 gw.devices.size= {}, device.queueSize= {}",
                        device.getGwno(), device.getDno(),
                        gw.getDevices().size(),
                        device.getQueueSize());
                }
            }
        } else {
            log.warn("[{}] 超时网关无设备....", timeoutGwno);
        }

        if (CollectionUtils.isEmpty(gw.getDevices())) {
            log.debug("[{}] 网关已无待采集的设备指令,可从采集队列移除", gw.getGwno());
            if (device != null
                && IotCollectionConstants.TCP_PROTOCOLS.contains(device.getProtocol())
                && gw.getNettyChannel() != null) {
                gw.getNettyChannel().close();   // 关闭TCP连接
            }
            gwRepo.removeGw(gw.getGwno());
        } else {
            // 发送下一个设备的采集指令
            launchingExecutor.sendNextRequest(gw.getGwno(), LocalDateTime.now());
        }

    }

    /**
     * @param gw
     * @param device
     * @return true表示设备已经无待发送的指令, 可从采集队列移除; false表示还有待发送的消息，需要发下一个；
     */
    private boolean handleModbusDeviceTimeout(ActiveGw gw, LaunchedModbusRtuDevice device) {
//        ModbusAddrRange addr =
        device.getReadQ().poll();   // 移除下发的采集地址
        // TODO: 通知超时
        if (CollectionUtils.isEmpty(device.getReadQ())) {
            return true;
        } else {
            return false;
        }
    }

    private boolean handleModbusTcpDeviceTimeout(ActiveGw gw, LaunchedModbusTcpDevice device) {
//        ModbusAddrRange addr =
        device.getReadQ().poll();   // 移除下发的采集地址
        // TODO: 通知超时
        if (CollectionUtils.isEmpty(device.getReadQ())) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * @param gw
     * @param device
     * @return true表示设备已经无待发送的指令, 可从采集队列移除; false表示还有待发送的消息，需要发下一个；
     */
    private boolean handleDlt645DeviceTimeout(ActiveGw gw, LaunchedDlt645Device device) {
//        ModbusAddrRange addr =
        if (SouthMsgType.READ == device.getCurMsgType()) {
            device.getReadQ().poll();   // 移除下发的采集地址
            // TODO: 通知超时
        } else if (SouthMsgType.WRITE == device.getCurMsgType()) {
            device.getWriteQ().poll();
            // TODO: 通知超时
        } else {
            log.warn("[{} {}] 超时处理异常. 下行指令类型错误 msgType= {}",
                gw.getGwno(), device.getDno(), device.getCurMsgType());
        }

        if (device.isEmpty()) {
            return true;
        } else {
            return false;
        }
    }
}
