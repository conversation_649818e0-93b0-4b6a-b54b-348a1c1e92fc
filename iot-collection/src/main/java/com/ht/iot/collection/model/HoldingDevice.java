package com.ht.iot.collection.model;

import com.cdz360.iot.model.modbus.type.Rs485Protocol;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.ht.iot.collection.model.dto.NotifyCfg;
import com.ht.iot.collection.model.type.ModbusTcpSocketType;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 待采集的设备
 */
@Data
@Accessors(chain = true)
public abstract class HoldingDevice {
    private Long id;

    private String gwno;

    private String dno;

    private Rs485Protocol protocol;



    /**
     * 发送下一次请求的时间
     */
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime nextTime;


    /**
     * 采集的间隔时间，单位秒
     */
    private Long duration;

    /**
     * 设备类型：水、电、气、秤
     */
    private String deviceType;

    /**
     * 超时等待时间，单位秒
     */
    private Long expireDur;

    /**
     * 下行（发送给设备）的mqtt topic
     */
    private String mqttDownTopic;

    /**
     * 推送采集数据的地址, 支持 http(s)://
     */
    private List<NotifyCfg> notifyCfg;


    /**
     * true,组包一起上报; false,采集到部分数据字段后立即上报。 默认false
     */
    private Boolean combineNotify;


    /**
     * false,停止采集; 其他都表示正常采集
     */
    private Boolean enable;
}
