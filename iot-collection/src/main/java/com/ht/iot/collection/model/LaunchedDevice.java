package com.ht.iot.collection.model;

import com.cdz360.iot.model.modbus.type.Rs485Protocol;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.ht.iot.collection.model.dto.NotifyCfg;
import com.ht.iot.collection.model.type.SouthMsgType;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 已发送采集信息的设备,等待响应消息
 */
@Data
@Accessors(chain = true)
public abstract class LaunchedDevice {

    private String gwno;

    private String dno;

    private Rs485Protocol protocol;

    /**
     * 当前发送的下行消息的类型
     */
    private SouthMsgType curMsgType;

    /**
     * 发送请求的时间
     */
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime reqTime;
    /**
     * 超时时间
     */
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime expireTime;

    /**
     * 采集的间隔时间，单位秒
     */
    private Long duration;

    /**
     * 超时等待时间，单位秒
     */
    private Long expireDur;

    /**
     * 下行（发送给设备）的mqtt topic
     */
    private String mqttDownTopic;

    /**
     * 推送采集数据的地址, 支持 http(s)://
     */
    private List<NotifyCfg> notifyCfg;

    /**
     * 设备类型：水、电、气、秤
     */
    private String deviceType;

    /**
     * true,组包一起上报; false,采集到部分数据字段后立即上报。 默认false
     */
    private Boolean combineNotify;

    /**
     * 采集到的数据缓存
     */
    private Map<String, Object> data;

    /**
     *
     * @return true，待采集/写指令的队列都为空
     */
    public abstract  boolean isEmpty();

    /**
     *
     * @return 待采集/写指令的队列长度
     */
    public abstract int getQueueSize();
}
