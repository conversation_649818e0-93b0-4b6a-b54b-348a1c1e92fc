package com.ht.iot.collection.model.dto;

import com.cdz360.iot.model.modbus.type.Rs485Protocol;
import java.util.List;
import javax.annotation.Nullable;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
public class UpdateDeviceInfoDto {

    @NotNull(message = "网关编号不能为空")
    private String gwno;

    /**
     * 设备编号. 空表示新设备
     */
    @Nullable
    private String dno;


    @NotNull(message = "通信协议不能为空")
    private Rs485Protocol protocol;

    @NotNull(message = "设备类型不能为空")
    private String deviceType;

    /**
     * 采集的间隔时间，单位秒
     */
    @NotNull(message = "采集间隔不能为空")
    private Long duration;

    /**
     * 超时等待时间，单位秒
     */
    private Long expireDur;


    /**
     * 下行（发送给设备）的mqtt topic
     */
    private String mqttDownTopic;

    /**
     * 队列最大长度
     */
    private Long qSize;

    /**
     * 推送采集数据的地址, 支持 http(s)://
     */
    private List<NotifyCfg> notifyCfg;

    /**
     * true,组包一起上报; false,采集到部分数据字段后立即上报。 默认false
     */
    private Boolean combineNotify;


    /**
     * false,停止采集; 其他都表示正常采集
     */
    private Boolean enable;
}
