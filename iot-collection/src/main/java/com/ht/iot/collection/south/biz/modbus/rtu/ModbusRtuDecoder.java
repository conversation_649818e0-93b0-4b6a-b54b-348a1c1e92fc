package com.ht.iot.collection.south.biz.modbus.rtu;

import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.utils.ByteUtils;
import com.cdz360.base.utils.DecimalUtils;
import com.cdz360.iot.model.ess.modbus.P485ResMsg;
import com.cdz360.iot.model.modbus.dto.ModbusAbstractTv;
import com.cdz360.iot.model.modbus.dto.ModbusAddrRange;
import com.cdz360.iot.model.modbus.dto.ModbusBcdTv;
import com.cdz360.iot.model.modbus.dto.ModbusDecimalTv;
import com.cdz360.iot.model.modbus.dto.ModbusIeee754Tv;
import com.cdz360.iot.model.modbus.dto.ModbusIntegerTv;
import com.cdz360.iot.model.modbus.dto.ModbusLongTv;
import com.cdz360.iot.model.modbus.dto.ModbusNumberTv;
import com.ht.iot.collection.utils.ModbusUtils;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class ModbusRtuDecoder {

    public static final int MODBUS_HEAD_LEN = 3;
    public static final int MODBUS_CRC_LEN = 2;

    int ERROR_REPLY_STEP_VALUE = 0x80; // 异常应答帧功能码 = 请求帧功能码 + 0x80

    /**
     * 解析modbus通用头, 校验CRC
     *
     * @return 返回数据部分的长度
     */
    public Integer parseHeader(String gwno, String dno, byte[] buf, int did, byte cmd) {
        if (buf == null) {
            throw new DcServiceException("获取设备信息失败");
        } else if (buf[0] != (byte) (did & 0xFF)) {
            throw new DcServiceException("解析modbus响应消息失败");
        } else if (buf[1] == (cmd + ERROR_REPLY_STEP_VALUE)) {
            throw new DcServiceException("获取设备信息失败(设备异常应答).");
        } else if (buf[1] != cmd) {
            throw new DcServiceException("获取设备信息失败..");
        }
        int length = buf[2] & 0xFF;
        int crc = ByteUtils.crc16(buf, 3 + length);
        int crcMsg = ByteUtils.bytes2IntLE(buf, 3 + length, 2);    // 报文里的CRC
        if (crc != crcMsg) {
            log.error("[{} {}] CRC校验不匹配. 报文crc = {}, 计算crc = {}, 报文 = {}",
                gwno, dno, crcMsg,
                crc, ByteUtils.bytesToHex(buf));
            throw new DcServiceException("CRC校验不匹配");
        }
        return length;
    }

//    public String parseAscii(String tid, String dno, byte[] buf, int deviceId, byte cmd) {
//        parseHeader(tid, dno, buf, deviceId,cmd);
//        int idx = 2;
//        int length = buf[idx++] & 0xFF;
//        return ByteUtils.bytes2ASCII(buf, idx, length);
//    }


    public ModbusIntegerTv parseInteger(String tid, String dno, byte[] msg,
        int idx,
        ModbusIntegerTv in) {
//        int idx = (in.getAddr() - msg.getAddr()) * 2;
        ModbusIntegerTv result = new ModbusIntegerTv();
        Integer v = null;
        int len = in.getNum() * 2;
        byte[] buf = ModbusUtils.orderBytes(tid, msg, idx, in.getOrder(), len);
        int idxTmp = 0;

//        if (Boolean.TRUE.equals(in.getRegisterSwitch())) {
//            buf = this.switchRegister2(buf, idxTmp);    // 将2个寄存的位置做对换
//            idxTmp = 0;
//        }
        if (Boolean.TRUE.equals(in.getSign())) {
            if (len == 2) {
                v = (int) ByteUtils.bytes2ShortLE(buf, idxTmp);
            } else if (len == 4) {
                v = ByteUtils.bytes2IntLE(buf, idxTmp, len);
            } else {
                log.error("[{} {}] 不支持的字段长度. len = {}", tid, dno, len);
            }
        } else {
            if (len == 2) {
                v = ByteUtils.bytes2IntLE(buf, idxTmp, len);
            } else if (len == 4) {
                v = (int) ByteUtils.bytes2LongLE(buf, idxTmp, len);
            } else {
                log.error("[{} {}] 不支持的字段长度. len = {}", tid, dno, len);
            }
        }
        if (v != null) {
            if (in.getMultiple() != null && !DecimalUtils.isZero(in.getMultiple())) {
                // 倍数
                v = new BigDecimal(v).multiply(in.getMultiple()).intValue();
            }
            if (in.getShift() != null && in.getShift() != 0) {
                v = v + in.getShift();
            }
        }
        result.setV(v);
        return result;
    }

    public ModbusIntegerTv parseBit(String tid, String dno, P485ResMsg msg,
        int byteIdx,
        int bitIdx,
        ModbusIntegerTv in) {
        ModbusIntegerTv result = new ModbusIntegerTv();
        byte b = msg.getBuf()[byteIdx];
        int v = b >> bitIdx & (byte) 0x01;
        result.setV(v);
        return result;
    }

    public ModbusIntegerTv parseBitByNum(String tid, String dno, P485ResMsg msg,
        int byteIdx,
        int bitIdx,
        ModbusIntegerTv in) {
        log.debug("[{}] parseBitByNum. byteIdx: {}, bitIdx: {}", tid, byteIdx, bitIdx);
        int num = in.getNum();
        ModbusIntegerTv result = new ModbusIntegerTv();
        int b = msg.getBuf()[byteIdx];
        if (bitIdx + num > 8) {
            b = ByteUtils.bytes2IntBE(msg.getBuf(), byteIdx, 2);
        }
        int v = b >> bitIdx & (byte) num;
        result.setV(v);
        return result;
    }

    public ModbusLongTv parseLong(String tid, String dno, byte[] msg,
        int idx,
        ModbusLongTv in) {
//        int idx = (in.getAddr() - msg.getAddr()) * 2;
        ModbusLongTv result = new ModbusLongTv();
        Long v = null;
        int len = in.getNum() * 2;
        byte[] buf = ModbusUtils.orderBytes(tid, msg, idx, in.getOrder(), len);
        int idxTmp = 0;
        if (Boolean.TRUE.equals(in.getSign())) {
            if (len == 2) {
                v = (long) ByteUtils.bytes2ShortLE(buf, idxTmp);
            } else if (len == 4) {
                v = (long) ByteUtils.bytes2IntLE(buf, idxTmp, len);
            } else {
                log.error("[{} {}] 不支持的字段长度. len = {}", tid, dno, len);
            }
        } else {
            if (len == 2) {
                v = (long) ByteUtils.bytes2IntLE(buf, idxTmp, len);
            } else if (len == 4) {
                v = ByteUtils.bytes2LongLE(buf, idxTmp, len);
            } else {
                log.error("[{} {}] 不支持的字段长度. len = {}", tid, dno, len);
            }
        }

        if (v != null) {
            if (in.getMultiple() != null && !DecimalUtils.isZero(in.getMultiple())) {
                // 倍数
                v = new BigDecimal(v).multiply(in.getMultiple()).longValue();
            }
            if (in.getShift() != null && in.getShift() != 0) {
                v = v + in.getShift();
            }
        }
        result.setV(v);
        return result;
    }

    public ModbusDecimalTv parseDecimal(String tid, String dno, byte[] msg,
        int idx,
        ModbusDecimalTv in) {
//        int idx = (in.getAddr() - msg.getAddr()) * 2;
        ModbusDecimalTv result = new ModbusDecimalTv();
        Long lv = null;
        int len = in.getNum() * 2;
        byte[] buf = ModbusUtils.orderBytes(tid, msg, idx, in.getOrder(), len);
        int idxTmp = 0;

        if (Boolean.TRUE.equals(in.getSign())) {
            if (len == 2) {
                lv = (long) ByteUtils.bytes2ShortLE(buf, idxTmp);
            } else if (len == 4) {
                lv = (long) ByteUtils.bytes2IntLE(buf, idxTmp, len);
            } else {
                log.error("[{} {}] 不支持的字段长度. len = {}", tid, dno, len);
            }
        } else {
            if (len == 2) {
                lv = (long) ByteUtils.bytes2IntLE(buf, idxTmp, len);
            } else if (len == 4) {
                lv = ByteUtils.bytes2LongLE(buf, idxTmp, len);
            } else {
                log.error("[{} {}] 不支持的字段长度. len = {}", tid, dno, len);
            }
        }
        if (lv != null) {
            if (in.getShift() != null && in.getShift() != 0) {
                lv = lv + in.getShift();
            }
            BigDecimal v = new BigDecimal(lv);
            if (in.getMultiple() != null && !DecimalUtils.isZero(in.getMultiple())) {
                // 倍数
                v = v.multiply(in.getMultiple());
            }
            if (in.getDecimal() != null) {
                BigDecimal divider = new BigDecimal(Math.pow(10, in.getDecimal()));

                v = v.divide(divider, in.getDecimal(),
                    RoundingMode.HALF_UP);
                result.setV(v);
            } else {
                result.setV(v);
            }
        } else {
            result.setV(null);
        }
        return result;
    }

    /**
     * BCD 解析数字
     */
    public ModbusBcdTv parseBcd(String tid, String dno, byte[] msg,
        int idx,
        ModbusBcdTv in) {
        ModbusBcdTv result = new ModbusBcdTv();
        log.debug("收到解析请求：msg = {}", msg);
        int len = in.getNum() * 2;
        byte[] buf = ModbusUtils.orderBytes(tid, msg, idx, in.getOrder(), len);
        log.debug("排序后结果：buf = {}", buf);
        Long lv = Long.parseLong(ByteUtils.bytesToHex(buf));

        log.debug("格式化后结果：lv = {}", lv);
        if (in.getShift() != null && in.getShift() != 0) {
            lv = lv + in.getShift();
        }
        BigDecimal v = new BigDecimal(lv);
        if (in.getMultiple() != null && !DecimalUtils.isZero(in.getMultiple())) {
            // 倍数
            v = v.multiply(in.getMultiple());
        }
        if (in.getDecimal() != null) {
            BigDecimal divider = new BigDecimal(Math.pow(10, in.getDecimal()));

            v = v.divide(divider, in.getDecimal(),
                RoundingMode.HALF_UP);
            result.setV(v);
        } else {
            result.setV(v);
        }

        log.debug("最终结果：res = {}", result);

        return result;
    }

    /**
     * 解析 IEEE 754 标准的浮点数
     */
    public ModbusIeee754Tv parseIeee754(String gwno, String dno, byte[] msg,
        int idx,
        ModbusIeee754Tv in) {
        ModbusIeee754Tv result = new ModbusIeee754Tv();
        int len = in.getNum() * 2;
        byte[] buf = ModbusUtils.orderBytes(gwno, msg, idx, in.getOrder(), len);

        if (buf.length == 4) {    // 单精度浮点数
            int iv = ByteUtils.bytes2IntLE(buf, 0, 4);
            BigDecimal v = new BigDecimal(Float.intBitsToFloat(iv));
            if (in.getMultiple() != null && !DecimalUtils.isZero(in.getMultiple())) {
                // 倍数
                v = v.multiply(in.getMultiple());
            }
            result.setV(v);
        } else if (buf.length == 8) {   // 双精度浮点数
            long lv = ByteUtils.bytes2LongLE(buf, 0, 8);
            BigDecimal v = new BigDecimal(Double.longBitsToDouble(lv));
            if (in.getMultiple() != null && !DecimalUtils.isZero(in.getMultiple())) {
                // 倍数
                v = v.multiply(in.getMultiple());
            }
            result.setV(v);
        } else {
            log.error("[{} {}] 字节长度异常. buf= {}",
                gwno, dno, ByteUtils.bytesToHex(buf));
            return null;
        }

        return result;
    }


    protected Integer getIntegerValue(String tid, String dno, Map<String, ModbusAbstractTv> tvs,
        String name) {
        ModbusAbstractTv tv = tvs.get(name);
        return getIntegerValue(tid, dno, tv, name);
    }

    protected Integer getIntegerValue(String tid, String dno, ModbusAbstractTv tv,
        String name) {

        if (tv != null && tv instanceof ModbusIntegerTv) {
            return ((ModbusIntegerTv) tv).getV();
        } else {
            log.warn("[{} {}] 类型错误. name = {}, tv = {}", tid, dno,
                name,
                tv);
            return null;
        }
    }

    protected Long getLongValue(String tid, String dno, Map<String, ModbusAbstractTv> tvs,
        String name) {
        ModbusAbstractTv tv = tvs.get(name);
        if (tv != null && tv instanceof ModbusLongTv) {
            return ((ModbusLongTv) tv).getV();
        } else {
            log.warn("[{} {}] 类型错误. name = {}, tv = {}", tid, dno,
                name,
                tv);
            return null;
        }
    }

    protected BigDecimal getDecimalValue(String tid, String dno, Map<String, ModbusAbstractTv> tvs,
        String name) {
        ModbusAbstractTv tv = tvs.get(name);
        return this.getDecimalValue(tid, dno, tv, name);
    }

    protected ModbusDecimalTv getDecimalTv(String tid, String dno,
        Map<String, ModbusAbstractTv> tvs,
        String name) {
        ModbusAbstractTv tv = tvs.get(name);
        if (tv != null && tv instanceof ModbusDecimalTv) {
            return (ModbusDecimalTv) tv;
        } else {
            log.warn("[{} {}] 类型错误. name = {}, tv = {}", tid, dno,
                name,
                tv);
            return null;
        }
    }

    protected BigDecimal getDecimalValue(String tid, String dno, ModbusAbstractTv tv,
        String name) {
//        ModbusAbstractTv tv = tvs.get(name);
        if (tv != null && tv instanceof ModbusDecimalTv) {
            return ((ModbusDecimalTv) tv).getV();
        } else {
            log.warn("[{} {}] 类型错误. name = {}, tv = {}", tid, dno,
                name,
                tv);
            return null;
        }
    }

    protected BigDecimal getIEEE75432Value(String tid, String dno, ModbusAbstractTv tv,
        String name) {
//        ModbusAbstractTv tv = tvs.get(name);
        if (tv != null && tv instanceof ModbusIeee754Tv) {
            return ((ModbusIeee754Tv) tv).getV();
        } else {
            log.warn("[{} {}] 类型错误. name = {}, tv = {}", tid, dno,
                name,
                tv);
            return null;
        }
    }

    /**
     * 将收到的modbus响应消息 resBuf 根据传入的地址和键值对做解析
     *
     * @param reqAddr     请求消息的寄存器地址和数量
     * @param tvList      订阅的键值对
     * @param resBuf      从机返回的响应消息
     * @param idxStartPos 响应消息 resBuf 的数据部分的开始位置
     */
    public Map<String, Object> parseModbusRes(String gwno, String dno,
        // LaunchedModbusRtuDevice device,
        ModbusAddrRange reqAddr,
        List<ModbusAbstractTv> tvList,
        byte[] resBuf,
        int idxStartPos) {
        Map<String, Object> result = new HashMap<>();
//        byte[] resBuf = ByteUtils.hexToBytes(resMsg);
//        String gwno = gw.getGwno();
//        String dno = device.getDno();
//
//        if (resBuf == null || resBuf.length < 1) {
//            device.getReadQ().poll();   // 移除第一个地址，已采集完成
//            device.setReqAddr(null);
//            log.warn("[{} {}] 采集数据失败", gwno, dno);
//            return result;
//        }

        Map<Integer, ModbusAbstractTv> tvs = tvList.stream()
            .collect(Collectors.toMap(ModbusAbstractTv::getAddr, o -> o));

//        ModbusAddrRange reqAddr = device.getReadQ().peek();
        int addr = reqAddr.getAddr();
        for (int i = 0; i < reqAddr.getNum(); i++) {
            ModbusAbstractTv tv = tvs.get(addr);
            if (tv != null) {
                int idx = idxStartPos + i * 2;
//                ModbusAbstractTv v = null;
                if (ModbusIntegerTv.TYPES.contains(tv.getT())) {
                    ModbusIntegerTv vx = this.parseInteger(gwno, dno, resBuf, idx,
                        (ModbusIntegerTv) tv);
                    result.put(tv.getName(), vx.getV());

                } else if (ModbusLongTv.TYPES.contains(tv.getT())) {
                    ModbusLongTv vx = this.parseLong(gwno, dno, resBuf, idx,
                        (ModbusLongTv) tv);
                    result.put(tv.getName(), vx.getV());
                } else if (ModbusBcdTv.TYPES.contains(tv.getT())) {
                    ModbusBcdTv vx = this.parseBcd(gwno, dno, resBuf, idx,
                        (ModbusBcdTv) tv);
                    result.put(tv.getName(), vx.getV());
                } else if (ModbusDecimalTv.TYPES.contains(tv.getT())) {
                    ModbusDecimalTv vx = this.parseDecimal(gwno, dno, resBuf, idx,
                        (ModbusDecimalTv) tv);
                    if (vx != null) {
                        result.put(tv.getName(), vx.getV());
                    }
                } else if (ModbusIeee754Tv.TYPES.contains(tv.getT())) {
                    ModbusIeee754Tv dv = this
                        .parseIeee754(gwno, dno, resBuf, idx, (ModbusIeee754Tv) tv);
                    if (dv != null) {
                        result.put(tv.getName(), dv.getV());
                    }
                } else {
                    log.error("[{} {}] 不支持的类型. tv = {}", gwno, dno, tv);
                }
//                if (v != null) {
//                    // 通用字段赋值
//                    v.setName(tv.getName())
//                        .setNum(tv.getNum())
//                        .setAddr(tv.getAddr())
//                        .setT(tv.getT())
//                        .setDisplayName(tv.getDisplayName())
//                        .setUnit(tv.getUnit())
//                        .setRw(tv.getRw())
//                        .setDesc(tv.getDesc());
//                    v.setBytes(new byte[v.getNum() * 2]);
//                    System.arraycopy(resBuf, idx, v.getBytes(), 0, v.getNum() * 2);
//
//                    if (v instanceof ModbusNumberTv) {
//                        ModbusNumberTv nv = (ModbusNumberTv) v;
//                        nv.setMinVal(((ModbusNumberTv) tv).getMinVal())
//                            .setMaxVal(((ModbusNumberTv) tv).getMaxVal())
//                            .setDecimal(((ModbusNumberTv) tv).getDecimal());
//                    }
//
//                }
                addr += tv.getNum();
                i = i + (tv.getNum() - 1);
            } else {
                // 不需要解析，跳到下一个寄存器
                addr += 1;
            }
        }
//        device.getReadQ().poll();   // 移除第一个地址，已采集完成
//        device.setReqAddr(null);
//        log.debug("[{} {}] 本次采集到的数据= {}", gwno, dno, JsonUtils.toJsonString(result));
        return result;
    }
}
