package com.cdz360.iot.device.mgm.ds.service;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.model.base.param.SortParam;
import com.cdz360.base.model.base.type.OrderType;
import com.cdz360.base.model.es.type.hi.EssType;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.iot.common.base.IotConstants;
import com.cdz360.iot.common.utils.CollectionUtils;
import com.cdz360.iot.common.utils.IotAssert;
import com.cdz360.iot.device.mgm.dzds.client.IotDzAuthCoreApiClient;
import com.cdz360.iot.device.mgm.model.basic.param.UploadBundleParam;
import com.cdz360.iot.device.mgm.utils.FileUtils;
import com.cdz360.iot.device.mgm.utils.ZipUtils;
import com.cdz360.iot.ds.ro.EvseBundlePcQueryDs;
import com.cdz360.iot.ds.ro.EvseBundleQueryDs;
import com.cdz360.iot.ds.ro.EvsePackageRoDs;
import com.cdz360.iot.ds.ro.EvseRoDs;
import com.cdz360.iot.ds.ro.UpgradeTaskDetailQueryDs;
import com.cdz360.iot.ds.ro.ess.ds.EssRoDs;
import com.cdz360.iot.ds.rw.EvseBundlePcOriginRwDs;
import com.cdz360.iot.ds.rw.EvseBundlePcRwDs;
import com.cdz360.iot.ds.rw.EvseBundleRwDs;
import com.cdz360.iot.ds.rw.EvsePackageRwDs;
import com.cdz360.iot.model.config.UpgDownloadProperties;
import com.cdz360.iot.model.ess.po.EssPo;
import com.cdz360.iot.model.evse.EvseBundle;
import com.cdz360.iot.model.evse.EvseBundleContext;
import com.cdz360.iot.model.evse.EvseBundlePc;
import com.cdz360.iot.model.evse.EvseBundlePcOrigin;
import com.cdz360.iot.model.evse.EvsePo;
import com.cdz360.iot.model.evse.PC0X;
import com.cdz360.iot.model.evse.UpgradePgContext;
import com.cdz360.iot.model.evse.dto.EvseBundleDto;
import com.cdz360.iot.model.evse.dto.EvseInfoDto;
import com.cdz360.iot.model.evse.dto.PC0XDto;
import com.cdz360.iot.model.evse.param.EvseBundleParam;
import com.cdz360.iot.model.evse.param.ListPackageParam;
import com.cdz360.iot.model.evse.param.UpdateEvsePackageParam;
import com.cdz360.iot.model.evse.param.UpgradeStatusParam;
import com.cdz360.iot.model.evse.type.BundleType;
import com.cdz360.iot.model.evse.type.EvseVendor;
import com.cdz360.iot.model.evse.upgrade.UpdateTaskStatusEnum;
import com.cdz360.iot.model.evse.vo.EvsePackageVo;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

/**
 * 海外平台升级包管理
 */
@Service
@Slf4j
@EnableConfigurationProperties(UpgDownloadProperties.class)
public class EvsePackageService {

    @Autowired
    private EvsePackageRwDs evsePackageRwDs;

    @Autowired
    private EvsePackageRoDs evsePackageRoDs;

    public BaseResponse deleteById(UpdateEvsePackageParam param) {
        evsePackageRwDs.updatePackage(param);
        return RestUtils.success();
    }

    public BaseResponse updateStatus(UpdateEvsePackageParam param) {
        evsePackageRwDs.updatePackage(param);
        return RestUtils.success();
    }

    public ListResponse<EvsePackageVo> getList(ListPackageParam param) {
        if (param.getSize() == null) {
            param.setSize(10);
        }
        if (param.getStart() == null) {
            param.setStart(0L);
        }
        List<EvsePackageVo> list = evsePackageRoDs.getList(param);
        return RestUtils.buildListResponse(list, evsePackageRoDs.getCount(param));
    }

    public BaseResponse  createPackage(UpdateEvsePackageParam params) {
        evsePackageRwDs.create(params);
        return RestUtils.success();
    }

    public ListResponse<String>  getBrandList() {
        return RestUtils.buildListResponse(evsePackageRoDs.getBrandList());
    }

    public BaseResponse editPackage(UpdateEvsePackageParam param) {
        evsePackageRwDs.updatePackage(param);
        return RestUtils.success();
    }
}
