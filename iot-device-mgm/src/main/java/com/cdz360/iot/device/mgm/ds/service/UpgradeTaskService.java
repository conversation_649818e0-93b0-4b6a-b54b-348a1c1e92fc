package com.cdz360.iot.device.mgm.ds.service;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.type.EvseStatus;
import com.cdz360.base.model.base.type.IotGwCmdType2;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.data.cache.model.IotGwCmdCacheVo;
import com.cdz360.iot.common.utils.CollectionUtils;
import com.cdz360.iot.common.utils.IotAssert;
import com.cdz360.iot.ds.GwInfoService;
import com.cdz360.iot.ds.ro.EvseBundlePcOriginQueryDs;
import com.cdz360.iot.ds.ro.EvseRoDs;
import com.cdz360.iot.ds.ro.UpgradeLogRoDs;
import com.cdz360.iot.ds.ro.UpgradeTaskDetailQueryDs;
import com.cdz360.iot.ds.ro.UpgradeTaskQueryDs;
import com.cdz360.iot.ds.ro.UpgradeTaskRelQueryDs;
import com.cdz360.iot.ds.ro.ess.ds.EssRoDs;
import com.cdz360.iot.ds.ro.mapper.SiteRoMapper;
import com.cdz360.iot.ds.rw.SequenceRwService;
import com.cdz360.iot.ds.rw.UpgradeLogRwDs;
import com.cdz360.iot.ds.rw.UpgradeTaskDetailRwDs;
import com.cdz360.iot.ds.rw.UpgradeTaskRelRwDs;
import com.cdz360.iot.ds.rw.UpgradeTaskRwDs;
import com.cdz360.iot.model.config.UpgDownloadProperties;
import com.cdz360.iot.model.ess.po.EssPo;
import com.cdz360.iot.model.evse.EvseBundle;
import com.cdz360.iot.model.evse.EvseBundleContext;
import com.cdz360.iot.model.evse.EvseBundlePc;
import com.cdz360.iot.model.evse.EvseBundleRemoteFileConf;
import com.cdz360.iot.model.evse.EvsePo;
import com.cdz360.iot.model.evse.PC0X;
import com.cdz360.iot.model.evse.dto.UpgradeTaskDto;
import com.cdz360.iot.model.evse.param.ListEvseParam;
import com.cdz360.iot.model.evse.param.StartUpgradeTaskParam;
import com.cdz360.iot.model.evse.po.PgPcItem;
import com.cdz360.iot.model.evse.type.BundleType;
import com.cdz360.iot.model.evse.type.UpgradeStatus;
import com.cdz360.iot.model.evse.upgrade.EvseFailReasonType;
import com.cdz360.iot.model.evse.upgrade.EvseUpgradeRequest;
import com.cdz360.iot.model.evse.upgrade.EvseVersion;
import com.cdz360.iot.model.evse.upgrade.UpdateTaskStatusEnum;
import com.cdz360.iot.model.evse.upgrade.UpgradeEvseResultEnum;
import com.cdz360.iot.model.evse.upgrade.UpgradeRecordVo;
import com.cdz360.iot.model.evse.upgrade.UpgradeTaskDetailRequest;
import com.cdz360.iot.model.evse.upgrade.UpgradeTaskDetailVo;
import com.cdz360.iot.model.evse.upgrade.UpgradeTaskInfoRequest;
import com.cdz360.iot.model.evse.upgrade.UpgradeTaskInfoVo;
import com.cdz360.iot.model.evse.upgrade.UpgradeTaskListRequest;
import com.cdz360.iot.model.evse.upgrade.UpgradeTaskRelVo;
import com.cdz360.iot.model.evse.upgrade.UpgradeTaskVo;
import com.cdz360.iot.model.gw.param.ListUpgradeLogParam;
import com.cdz360.iot.model.gw.po.UpgradeLogPo;
import com.cdz360.iot.model.gw.vo.UpgradeLogVo;
import com.cdz360.iot.model.site.po.SitePo;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

/**
 * @Classname UpgradeTaskService
 * @Description TODO
 * @Date 9/18/2019 2:51 PM
 * @Created by Rafael
 */
@Service
@EnableConfigurationProperties(UpgDownloadProperties.class)
public class UpgradeTaskService {

    private final Logger logger = LoggerFactory.getLogger(UpgradeTaskService.class);

    private static final String KEY_SW_VER = "KEY_SW_VER";
    private static final String KEY_HW_VER = "KEY_HW_VER";
    private static final String KEY_VENDER_CODE = "KEY_VENDER_CODE";

//    @Autowired
//    private MqttService mqttService;

    @Autowired
    private MqService mqService;

    @Autowired
    private UpgradeTaskQueryDs upgradeTaskQueryDs;
    @Autowired
    private UpgradeTaskRwDs upgradeTaskRwDs;
    @Autowired
    private UpgradeTaskDetailQueryDs upgradeTaskDetailQueryDs;
    @Autowired
    private UpgradeTaskDetailRwDs upgradeTaskDetailRwDs;
    @Autowired
    private UpgradeTaskRelQueryDs upgradeTaskRelQueryDs;
    @Autowired
    private UpgradeTaskRelRwDs upgradeTaskRelRwDs;
    @Autowired
    private EvseBundlePcOriginQueryDs evseBundlePcOriginQueryDs;
    @Autowired
    private GwInfoService gwInfoService;

    @Autowired
    private UpgDownloadProperties upgDownloadProperties;

    @Autowired
    private SiteRoMapper siteRoMapper;
    @Autowired
    private EvseRoDs evseRoDs;

    @Autowired
    private EssRoDs essRoDs;

    @Autowired
    private EvseBundleService evseBundleService;

    @Autowired
    private UpgradeLogRoDs upgradeLogRoDs;

    @Autowired
    private UpgradeLogRwDs upgradeLogRwDs;

    @Autowired
    private SequenceRwService sequenceRwService;

    public ListResponse<UpgradeTaskVo> getUpgradeTaskListBySite(String siteId,
        Long taskId,
        String bundleKeyword,
        Integer index,
        Integer size) {

        Integer start = null;
        Integer end = null;
        if (index == null || size == null) {
        } else {
            start = (index - 1) * size;
            end = index * size;
        }
        List<UpgradeTaskVo> list = upgradeTaskQueryDs.select(siteId, taskId, bundleKeyword, start,
            end);
        return new ListResponse<>(list,
            upgradeTaskQueryDs.selectCount(siteId, taskId, bundleKeyword));
    }

    public ListResponse<UpgradeTaskDetailVo> getUpgradeTaskDetailListByTaskId(
        UpgradeTaskDetailRequest updateTaskDetailRequest) {
        List<UpgradeTaskDetailVo> list = upgradeTaskDetailQueryDs.select(
            updateTaskDetailRequest.getTaskId(),
            updateTaskDetailRequest.getEvseNo(),
            updateTaskDetailRequest.getStatus()
        );

        return new ListResponse<>(list, Long.valueOf(list.size()));

    }

    public ObjectResponse<UpgradeTaskInfoVo> getUpgradeTaskInfo(
        UpgradeTaskInfoRequest upgradeTaskInfoRequest) {
        UpgradeTaskInfoVo upgradeTaskDetailVo = upgradeTaskQueryDs
            .getUpgradeTaskInfo(upgradeTaskInfoRequest.getTaskId());

        Assert.isTrue(upgradeTaskDetailVo != null,
            "找不到任务: " + upgradeTaskInfoRequest.getTaskId());
        evseBundleService.selectEvseBundlePcList(upgradeTaskDetailVo.getBundleId()).stream()
            .forEach(e -> {
                if (e.getPcName().equalsIgnoreCase("pc01")) {
                    upgradeTaskDetailVo.setPc01Ver(String.format("%s-%s-%s",
                        e.getHwVer(),
                        e.getSwVer(),
                        e.getVendorCode()));
                }
            });

        return new ObjectResponse<>(upgradeTaskDetailVo);
    }

    public ListResponse<UpgradeRecordVo> getUpgradeRecordVo(UpgradeTaskListRequest req) {
        Integer start = null;
        Integer size = null;
        if (req.getIndex() == null || req.getSize() == null) {
        } else {
            start = (req.getIndex() - 1) * req.getSize();
            size = req.getSize();
        }
        return upgradeTaskQueryDs.getUpgradeRecordVo(req.getEvseNo(), req.getTaskId(),
            req.getBundleKeyword(), start, size);
    }

    //    @Transactional
    public boolean restartTask(Long taskId, List<String> evseIds) {
        Assert.isTrue(taskId != null, "请传入taskId");
        Assert.isTrue(evseIds != null && !evseIds.isEmpty(), "请传入需要重新升级的桩");
        UpgradeTaskVo upgradeTaskVo = upgradeTaskQueryDs.selectById(taskId);
        Assert.isTrue(upgradeTaskVo != null, "找不到升级记录id: " + taskId);

        // 检查：重复的桩编号
        List<String> checkDuplicate = evseIds
            .stream()
            .filter(e -> Collections.frequency(evseIds, e) > 1)
            .distinct()
            .collect(Collectors.toList());
        Assert.isTrue(checkDuplicate.isEmpty(), "发现重复的桩编号，请检查。桩号: "
            + String.join(", ", checkDuplicate));

//        List<EvseBundlePcOrigin> evseBundlePcOriginList = evseBundlePcOriginQueryDs.selectByBundleId(upgradeTaskVo.getBundleId());
//        List<String> supportVer = evseBundlePcOriginList
//                .stream()
//                .map(EvseBundlePcOrigin::getOrigin)
//                .collect(Collectors.toList());

//        List<EvseBundlePcOrigin> pc01Deny = new ArrayList<>();
//        List<EvseBundlePcOrigin> pc02Deny = new ArrayList<>();
//        List<EvseBundlePcOrigin> pc03Deny = new ArrayList<>();

        List<UpgradeTaskDetailVo> upgradeTaskDetailVoList = upgradeTaskDetailQueryDs.select(taskId,
            null, null);
        Assert.isTrue(upgradeTaskDetailVoList != null && !upgradeTaskDetailVoList.isEmpty(),
            "找不到任何升级详情");

        //TODO 升级包、桩升级检查
        EvseBundle evseBundle = evseBundleService.selectByPrimaryKey(upgradeTaskVo.getBundleId());
        IotAssert.isTrue(evseBundle != null, "没有找到包详情信息，该包可能被删除，请确认。");
        List<EvseBundlePc> evseBundlePcList = evseBundleService.selectEvseBundlePcList(
            upgradeTaskVo.getBundleId());
        IotAssert.isTrue(!evseBundlePcList.isEmpty(),
            "没有找到模块详情信息，该包可能被删除，请确认。");

        // 检查：在请求升级的桩列表中，找出不在升级详情的桩
        List<String> excludeUpgradeDetailList = evseIds.stream()
            .filter(e ->
                !upgradeTaskDetailVoList
                    .stream()
                    .map(UpgradeTaskDetailVo::getEvseId)
                    .collect(Collectors.toList())
                    .contains(e))
            .collect(Collectors.toList());
        Assert.isTrue(excludeUpgradeDetailList.isEmpty(),
            "升级记录中找不到这些桩: " + String.join(", ", excludeUpgradeDetailList));

        // 检查：这些桩的升级状态应该是FAIL时，才能发起再升级，否则不允许再升级
        // Map: evseId -> UpgradeTaskDetailVo
        Map<String, UpgradeTaskDetailVo> upgradeTaskDetailVoMap = upgradeTaskDetailVoList
            .stream()
            .collect(Collectors.toMap(UpgradeTaskDetailVo::getEvseId, o -> o));
        List<String> evseUpgradeDetailNoFailStatusList = evseIds.stream()
            .filter(e -> upgradeTaskDetailVoMap.get(e).getStatus() != UpdateTaskStatusEnum.FAIL)
            .collect(Collectors.toList());
        Assert.isTrue(evseUpgradeDetailNoFailStatusList.isEmpty(),
            "升级状态不正确，可能发生变化，请刷新页面后再试。桩号：" +
                String.join(", ", evseUpgradeDetailNoFailStatusList));

        // 场站下所有桩
        ListEvseParam param = new ListEvseParam();
        param.setSiteId(upgradeTaskVo.getSiteId());
        List<EvsePo> evsePoInSite = evseRoDs.getEvseList(param);
        IotAssert.isTrue(!evsePoInSite.isEmpty(), "场站下找不到任何桩");
        List<String> allEvseIdsInSite = evsePoInSite.stream().map(EvsePo::getEvseId)
            .collect(Collectors.toList());

        List<String> excludeEvse = evseIds.stream().filter(e -> !allEvseIdsInSite.contains(e))
            .collect(Collectors.toList());
        IotAssert.isTrue(excludeEvse.isEmpty(),
            String.format("升级失败，场站%s下，找不到桩[%s]", upgradeTaskVo.getSiteId(),
                String.join(", ", excludeEvse)));

        // 待升级的桩
        List<EvsePo> evseToUpgrade = evsePoInSite.stream()
            .filter(e -> evseIds.contains(e.getEvseId()))
            .collect(Collectors.toList());

        Assert.isTrue(!evseToUpgrade.isEmpty(), "找不到待符合条件的待升级桩");

        // 桩升级失败原因
        Set<EvseFailReasonType> failReason = new HashSet<>();

        //TODO 列表太大可能需要异步处理，但考虑现在每个场站的桩不会太多，所以先这么写
        List<EvsePo> actualSendEvse = new ArrayList<>();//实际下发更新的桩号
        evseToUpgrade.stream().forEach(e -> {
            UpgradeTaskDetailVo upgradeTaskDetailVo = upgradeTaskDetailVoMap.get(e.getEvseId());

            // 记录升级当时的桩PC版本信息
            upgradeTaskDetailVo.setPc01Ver(e.getPc01Ver());
            upgradeTaskDetailVo.setPc02Ver(e.getPc02Ver());
            upgradeTaskDetailVo.setPc03Ver(e.getPc03Ver());

            // TODO 升级中的设备需要防止其升级，升级中的桩如果用相同的包再去升级，应该防止其发送记录生成
            if (!upgradeTaskDetailQueryDs.select(null, e.getEvseId(),
                List.of(UpdateTaskStatusEnum.UPDATING)).isEmpty()) {
                upgradeTaskDetailVo.setStatus(UpdateTaskStatusEnum.FAIL);
                upgradeTaskDetailVo.setFailReason("设备当前升级中");
                failReason.add(EvseFailReasonType.UPDATING);
            } else {
                switch (e.getEvseStatus()) {
                    case UNKNOWN:
                        upgradeTaskDetailVo.setStatus(UpdateTaskStatusEnum.FAIL);
                        upgradeTaskDetailVo.setFailReason("设备状态未知");
                        break;
                    case CONNECT:
                        upgradeTaskDetailVo.setStatus(UpdateTaskStatusEnum.FAIL);
                        upgradeTaskDetailVo.setFailReason("设备空占");
                        break;
                    case IDLE:
                        upgradeTaskDetailVo.setStatus(UpdateTaskStatusEnum.UPDATING);
                        upgradeTaskDetailVo.setFailReason("");
                        actualSendEvse.add(e);
                        break;
                    case BUSY:
                        upgradeTaskDetailVo.setStatus(UpdateTaskStatusEnum.FAIL);
                        upgradeTaskDetailVo.setFailReason("设备充电中");
                        break;
                    case ERROR:
                        upgradeTaskDetailVo.setStatus(UpdateTaskStatusEnum.FAIL);
                        upgradeTaskDetailVo.setFailReason("设备故障");
                        break;
                    case OFFLINE:
                        upgradeTaskDetailVo.setStatus(UpdateTaskStatusEnum.FAIL);
                        upgradeTaskDetailVo.setFailReason("设备离线");
                        break;
                    case OFF:
                        upgradeTaskDetailVo.setStatus(UpdateTaskStatusEnum.FAIL);
                        upgradeTaskDetailVo.setFailReason("设备下线");
                        break;
                }
                if (!EvseStatus.IDLE.equals(e.getEvseStatus())) {
                    failReason.add(
                        EvseFailReasonType.valueOf(e.getEvseStatus().name().toUpperCase()));
                }
            }
            upgradeTaskDetailVo.setUpdateTime(new Date());
            upgradeTaskDetailRwDs.update(upgradeTaskDetailVo);
        });

        return sendTaskToMq(upgradeTaskVo.getId(), actualSendEvse, evseBundle, evseBundlePcList,
            failReason);

//        Assert.isTrue(1==2, "you are bad");
    }

    private boolean sendTaskToMq(Long taskId,
        List<EvsePo> actualSendEvse,
        EvseBundle evseBundle,
        List<EvseBundlePc> evseBundlePcList,
        Set<EvseFailReasonType> failReasonTypes) {
        logger.info("发送mq,taskId={}, actualSendEvse={}, evseBundlePcList={}",
            taskId, actualSendEvse, evseBundlePcList);
        boolean boo = false;

        if (CollectionUtils.isEmpty(actualSendEvse)) {
            // 没有符合升级条件的桩
            if (CollectionUtils.isEmpty(failReasonTypes)) {
                IotAssert.isTrue(false, "未知原因导致的失败，请联系平台客服");
            } else if (failReasonTypes.size() > 1) {
                IotAssert.isTrue(false,
                    "所选定的桩当前不符合升级条件，请先确认桩的状态是否空闲，且桩没有在升级，然后再试");
            } else if (failReasonTypes.size() == 1) {
                EvseFailReasonType failReasonType = failReasonTypes.iterator().next();
                switch (failReasonType) {
                    case UNKNOWN:
                    case CONNECT:
                    case BUSY:
                    case ERROR:
                    case OFFLINE:
                    case OFF:
                        IotAssert.isTrue(false,
                            "所选定的桩当前不符合升级条件，请先确认桩的状态是否是空闲，然后再尝试");
                        break;
                    case UPDATING:
                        IotAssert.isTrue(false,
                            "所选定的桩当前不符合升级条件，请先确认桩是否正在升级，升级中不可重复操作");
                        break;
                }
            }
        }
//        IotAssert.isTrue(CollectionUtils.isNotEmpty(actualSendEvse),
//                "所选定的桩当前不符合升级条件。可能当前处于升级中。请确认后再尝试。");

        if (actualSendEvse != null && !actualSendEvse.isEmpty()) {
            Map<String, List<EvsePo>> gwnoMap = new HashMap<>();
            actualSendEvse.stream().forEach(e -> {
                if (gwnoMap.get(e.getGwno()) == null) {
                    gwnoMap.put(e.getGwno(), new ArrayList<>());
                }
                gwnoMap.get(e.getGwno()).add(e);
            });

            // 使用MQ取代了MQTT，以下不需要执行
//            // 检查： 网关是否在线，连接上了mqtt，有一个连不上，本次升级失败
//            List<String> abnormalGwnoList = gwnoMap.entrySet().stream().map(e -> e.getKey()).filter(e -> {
//                GwInfoDto gwInfoDto = gwInfoService.getByGwno(e, false);
//                Assert.isTrue(gwInfoDto != null, "找不到网关。编号: " + e);
//                return gwInfoDto.getStatus() != GwStatus.NORMAL;
//            }).collect(Collectors.toList());
//            Assert.isTrue(abnormalGwnoList.isEmpty(), "网关状态异常，请检查网关。编号: "
//                    + String.join(", ", abnormalGwnoList));

            // 生成下发记录
            UpgradeTaskRelVo upgradeTaskRelVo = new UpgradeTaskRelVo();
            upgradeTaskRelVo.setTaskId(taskId);
            upgradeTaskRelVo.setEvseIds(
                actualSendEvse.stream().map(EvsePo::getEvseId).collect(Collectors.joining(",")));
            upgradeTaskRelRwDs.insert(upgradeTaskRelVo);

            EvseBundleContext evseBundleContext = null;
            if (StringUtils.isNotBlank(evseBundle.getContext())) {
                evseBundleContext = JsonUtils.fromJson(evseBundle.getContext(),
                    EvseBundleContext.class);
            }

            EvseBundleRemoteFileConf remoteFileConf = getValidateRemoteFileConf(evseBundleContext);
            final Map<String, String> remoteFileNameMap = (remoteFileConf != null &&
                CollectionUtils.isNotEmpty(evseBundleContext.getPcList())) ?
                evseBundleContext.getPcList()
                    .stream()
                    .collect(Collectors.toMap(PC0X::getType,
                        PC0X::getRemoteFilePath,
                        (o, n) -> o)) :
                new HashMap<>();

            // 根据网关编号，分发升级请求到各个网关
            logger.info("发送远程升级网关映射表: {}", JsonUtils.toJsonString(gwnoMap));
            gwnoMap.forEach((gwno, v) -> {
                EvseUpgradeRequest evseUpgradeRequest = new EvseUpgradeRequest();
                evseUpgradeRequest.setSeq(sequenceRwService.getNextOutRequestSeq());
                evseUpgradeRequest.setTaskNo(upgradeTaskRelVo.getId().toString());
//                evseUpgradeRequest.setEvseIds(v.stream().map(EvsePo::getEvseId).collect(Collectors.toList()));

                List<EvseVersion> evseVersionList = new ArrayList<>();
                evseBundlePcList.stream().forEach(module -> {
                    evseUpgradeRequest.setDownloadType(
                        upgDownloadProperties.getSchema().toUpperCase());
                    EvseVersion evseVersion = new EvseVersion();
                    evseVersion.setName(module.getPcName());
                    evseVersion.setSwVer(module.getSwVer());
                    evseVersion.setVendorCode(module.getVendorCode());
                    if (remoteFileConf != null &&
                        StringUtils.isNotBlank(remoteFileNameMap.get(module.getPcName()))) {
                        evseVersion.setUrl(upgDownloadProperties.getDownloadPath(remoteFileConf,
                            remoteFileNameMap.get(module.getPcName())));
                    } else {
                        evseVersion.setUrl(upgDownloadProperties.getDownloadPath(module.getPath()));
                    }
                    evseVersionList.add(evseVersion);
                });
                if (remoteFileConf != null) {
                    evseUpgradeRequest.setDownloadUsername(remoteFileConf.getUserName());
                    evseUpgradeRequest.setDownloadPasscode(remoteFileConf.getUserPasscode());
                } else {
                    evseUpgradeRequest.setDownloadUsername(
                        upgDownloadProperties.getUsername(evseBundle.getVendor()));
                    evseUpgradeRequest.setDownloadPasscode(
                        upgDownloadProperties.getPassword(evseBundle.getVendor()));
                }
                evseUpgradeRequest.setEvseBundle(evseVersionList);
//                sendToMqtt(gwno, evseUpgradeRequest);
                v.stream().forEach(e -> {
                    evseUpgradeRequest.setEvseNo(e.getEvseId());
                    sendToMq(gwno, evseUpgradeRequest);
                });
            });
            boo = true;
        }
        return boo;
    }

    private EvseBundleRemoteFileConf getValidateRemoteFileConf(EvseBundleContext ctx) {
        if (ctx == null || ctx.getRemoteFileConf() == null) {
            return null;
        }
        EvseBundleRemoteFileConf conf = ctx.getRemoteFileConf();
        if (StringUtils.isBlank(conf.getHost()) ||
            StringUtils.isBlank(conf.getProtocol()) ||
            conf.getPort() == null) {
            logger.error("远程文件配置无效: {}", JsonUtils.toJsonString(conf));
            return null;
        }
        return conf;
    }

    @Transactional
    public ObjectResponse<UpgradeTaskDto> startUpgradeTask(StartUpgradeTaskParam taskParam) {
        Long bundleId = taskParam.getUpgradePgId();
        IotAssert.isTrue(StringUtils.isNotBlank(taskParam.getEssDno()), "请提供ESS设备编号");
        IotAssert.isNotNull(bundleId, "请提供升级包ID");

        // 设备是否存在
        EssPo ess = essRoDs.getByDno(taskParam.getEssDno());
        IotAssert.isNotNull(ess, "ESS设备编号无效");

        // 升级包是否存在
        EvseBundle pgInfo = evseBundleService.selectByPrimaryKey(bundleId);
        IotAssert.isNotNull(pgInfo, "没有找到包详情信息");
        List<EvseBundlePc> pgPcList = evseBundleService.selectEvseBundlePcList(bundleId);
        IotAssert.isTrue(!pgPcList.isEmpty(), "没有找到模块详情信息");

        // 是否在升级中
//        List<UpgradeTaskDetailVo> upgradingList = upgradeTaskDetailQueryDs.select(null,
//            taskParam.getEssDno(), List.of(UpdateTaskStatusEnum.UPDATING));
        ListUpgradeLogParam listUpgradeLogParam = new ListUpgradeLogParam()
            .setGwno(taskParam.getEssDno())
            .setStatusList(List.of(UpgradeStatus.CMD_RECEIVE));
        listUpgradeLogParam.setSize(1);
        List<UpgradeLogVo> upgradingList = upgradeLogRoDs.upgradeLogList(listUpgradeLogParam);
        IotAssert.isTrue(CollectionUtils.isEmpty(upgradingList), "设备在升级中，请稍后重试");

        UpgradeTaskVo upgradeTaskVo = new UpgradeTaskVo();
        upgradeTaskVo.setSiteId(taskParam.getEssDno());
        upgradeTaskVo.setBundleId(bundleId);
        upgradeTaskVo.setOpId(taskParam.getOpId());
        upgradeTaskVo.setOpName(taskParam.getOpName());
        upgradeTaskVo.setEvseCount(1);
        upgradeTaskRwDs.insert(upgradeTaskVo);

//        UpgradeTaskDetailVo upgradeTaskDetailVo = new UpgradeTaskDetailVo();
//        upgradeTaskDetailVo.setEvseId(taskParam.getEssDno());
//        upgradeTaskDetailVo.setTaskId(upgradeTaskVo.getId());

        // 记录升级当时的桩PC版本信息
//        upgradeTaskDetailVo.setPc01Ver(e.getPc01Ver());
//        upgradeTaskDetailVo.setPc02Ver(e.getPc02Ver());
//        upgradeTaskDetailVo.setPc03Ver(e.getPc03Ver());
//        upgradeTaskDetailVo.setStatus(UpdateTaskStatusEnum.UPDATING);
//        upgradeTaskDetailVo.setUpdateTime(new Date());
//        upgradeTaskDetailRwDs.insert(upgradeTaskDetailVo);

        UpgradeLogPo upgradeLogPo = new UpgradeLogPo();
        upgradeLogPo.setUpgradeStatus(UpgradeStatus.CMD_SEND)
            .setBundleId(bundleId)
            .setDownTime(new Date())
            .setBundleType(BundleType.USER_ESS)
            .setDeviceNo(taskParam.getEssDno());
        upgradeLogRwDs.insertUpgradeLog(upgradeLogPo);

        return RestUtils.buildObjectResponse(new UpgradeTaskDto()
            .setEssDno(taskParam.getEssDno())
            .setUpgradePgId(taskParam.getUpgradePgId())
            .setUpgradeLogId(upgradeLogPo.getId())
//            .setFetchAcc(upgDownloadProperties.getUsername(pgInfo.getVendor()))
//            .setFetchPassw(upgDownloadProperties.getPassword(pgInfo.getVendor()))
            .setPcItemList(pgPcList.stream().map(x -> new PgPcItem()
                .setSw(x.getSwVer()).setPath(x.getPath())
//                .setPath(upgDownloadProperties.getDownloadPath(x.getPath()))
                .setBinName(x.getPcName())).collect(Collectors.toList())));
    }

    @Transactional
    public boolean startTask(String siteId, List<String> evseIds, Long bundleId, Long opId,
        String OpName) {

        IotAssert.isTrue(!evseIds.isEmpty(), "请传入需要升级的桩列表");

        //TODO 升级包、桩升级检查
        //......待实现

        // 升级包是否存在
        EvseBundle evseBundle = evseBundleService.selectByPrimaryKey(bundleId);
        IotAssert.isTrue(evseBundle != null, "没有找到包详情信息");
        List<EvseBundlePc> evseBundlePcList = evseBundleService.selectEvseBundlePcList(bundleId);
        IotAssert.isTrue(!evseBundlePcList.isEmpty(), "没有找到模块详情信息");

        // 场站id是否存在
        SitePo sitePo = siteRoMapper.getSite(siteId);
        IotAssert.isTrue(sitePo != null, "找不到场站信息: " + siteId);

        // 过滤重复的桩号
        Set<String> duplicateEvseNoSet = evseIds.stream()
            .filter(e -> Collections.frequency(evseIds, e) > 1)
            .collect(Collectors.toSet());
        if (!duplicateEvseNoSet.isEmpty()) {
            logger.warn("存在重复的桩编号{}，此处忽略。", duplicateEvseNoSet);
        }
        List<String> evseIdsDistinct = evseIds.stream().distinct().collect(Collectors.toList());

        //场站下所有桩
//        List<EvseVo> evseInSite = redisIotReadService.listEvseBySiteId(siteId);

        ListEvseParam param = new ListEvseParam();
        param.setSiteId(siteId);
        List<EvsePo> evsePoInSite = evseRoDs.getEvseList(param);

//        List<EvsePo> evsePoList = evseQueryDs.selectBySiteId(siteId);
        IotAssert.isTrue(!evsePoInSite.isEmpty(), "场站下找不到任何桩");

        // 场站下所有的桩编号
        List<String> allEvseIdsInSite = evsePoInSite.stream().map(EvsePo::getEvseId)
            .collect(Collectors.toList());

        List<String> excludeEvse = evseIdsDistinct.stream()
            .filter(e -> !allEvseIdsInSite.contains(e))
            .collect(Collectors.toList());
        IotAssert.isTrue(excludeEvse.isEmpty(),
            String.format("升级失败，场站%s下，找不到桩[%s]", siteId,
                String.join(", ", excludeEvse)));

        // 待升级的桩
        List<EvsePo> evseToUpgrade = evsePoInSite.stream()
            .filter(e -> evseIdsDistinct.contains(e.getEvseId()))
            .collect(Collectors.toList());

        Assert.isTrue(!evseToUpgrade.isEmpty(), "找不到待符合条件的待升级桩");

        UpgradeTaskVo upgradeTaskVo = new UpgradeTaskVo();
        upgradeTaskVo.setSiteId(siteId);
        upgradeTaskVo.setBundleId(bundleId);
        upgradeTaskVo.setOpId(opId);
        upgradeTaskVo.setOpName(OpName);
        upgradeTaskVo.setEvseCount(evseToUpgrade.size());
        upgradeTaskRwDs.insert(upgradeTaskVo);

        // 桩升级失败原因
        Set<EvseFailReasonType> failReason = new HashSet<>();

        //TODO 列表太大可能需要异步处理，但考虑现在每个场站的桩不会太多，所以先这么写
        List<EvsePo> actualSendEvse = new ArrayList<>();//实际下发更新的桩号
        evseToUpgrade.stream().forEach(e -> {
            UpgradeTaskDetailVo upgradeTaskDetailVo = new UpgradeTaskDetailVo();
            upgradeTaskDetailVo.setEvseId(e.getEvseId());
            upgradeTaskDetailVo.setTaskId(upgradeTaskVo.getId());

            // 记录升级当时的桩PC版本信息
            upgradeTaskDetailVo.setPc01Ver(e.getPc01Ver());
            upgradeTaskDetailVo.setPc02Ver(e.getPc02Ver());
            upgradeTaskDetailVo.setPc03Ver(e.getPc03Ver());

            // TODO 升级中的设备需要防止其升级，升级中的桩如果用相同的包再去升级，应该防止其发送记录生成
            if (!upgradeTaskDetailQueryDs.select(null, e.getEvseId(),
                List.of(UpdateTaskStatusEnum.UPDATING)).isEmpty()) {
                upgradeTaskDetailVo.setStatus(UpdateTaskStatusEnum.FAIL);
                upgradeTaskDetailVo.setFailReason("设备当前升级中");
                failReason.add(EvseFailReasonType.UPDATING);
            } else {
                switch (e.getEvseStatus()) {
                    case UNKNOWN:
                        upgradeTaskDetailVo.setStatus(UpdateTaskStatusEnum.FAIL);
                        upgradeTaskDetailVo.setFailReason("设备状态未知");
                        break;
                    case CONNECT:
                        upgradeTaskDetailVo.setStatus(UpdateTaskStatusEnum.FAIL);
                        upgradeTaskDetailVo.setFailReason("设备空占");
                        break;
                    case IDLE:
                        upgradeTaskDetailVo.setStatus(UpdateTaskStatusEnum.UPDATING);
                        actualSendEvse.add(e);
                        break;
                    case BUSY:
                        upgradeTaskDetailVo.setStatus(UpdateTaskStatusEnum.FAIL);
                        upgradeTaskDetailVo.setFailReason("设备充电中");
                        break;
                    case ERROR:
                        upgradeTaskDetailVo.setStatus(UpdateTaskStatusEnum.FAIL);
                        upgradeTaskDetailVo.setFailReason("设备故障");
                        break;
                    case OFFLINE:
                        upgradeTaskDetailVo.setStatus(UpdateTaskStatusEnum.FAIL);
                        upgradeTaskDetailVo.setFailReason("设备离线");
                        break;
                    case OFF:
                        upgradeTaskDetailVo.setStatus(UpdateTaskStatusEnum.FAIL);
                        upgradeTaskDetailVo.setFailReason("设备下线");
                        break;
                }
                if (!EvseStatus.IDLE.equals(e.getEvseStatus())) {
                    failReason.add(
                        EvseFailReasonType.valueOf(e.getEvseStatus().name().toUpperCase()));
                }
            }
            upgradeTaskDetailVo.setUpdateTime(new Date());
            upgradeTaskDetailRwDs.insert(upgradeTaskDetailVo);
        });

        return sendTaskToMq(upgradeTaskVo.getId(), actualSendEvse, evseBundle, evseBundlePcList,
            failReason);
    }

    @Transactional
    public void upgradeEvseResult(String evseId, String taskNo, UpgradeEvseResultEnum result) {
        IotAssert.isNotBlank(evseId, "请输入evseId");
        IotAssert.isNotBlank(taskNo, "请输入taskNo");
        try {
            Long.valueOf(taskNo);
        } catch (Exception e) {
            throw new IllegalArgumentException("taskNo不正确");
        }
        IotAssert.isTrue(result != null, "请输入result");

        UpgradeTaskRelVo upgradeTaskRelVo = upgradeTaskRelQueryDs.selectById(Long.valueOf(taskNo));
        IotAssert.isTrue(upgradeTaskRelVo != null, "没有找到升级记录");

        List<String> updateEvseList = Arrays.asList(upgradeTaskRelVo.getEvseIds().split(","));
        IotAssert.isTrue(updateEvseList.contains(evseId), "升级记录中未找到该桩taskNo=" + taskNo);

        // taskId + 桩号evseNo，可以确定t_update_task_detail的记录，需要更新这个表的信息
        Long taskId = upgradeTaskRelVo.getTaskId();

        List<UpgradeTaskDetailVo> upgradeTaskDetailList = upgradeTaskDetailQueryDs.select(taskId,
            evseId, null);
        logger.info("查找到的升级详情{}", JsonUtils.toJsonString(upgradeTaskDetailList));
        IotAssert.isTrue(upgradeTaskDetailList.size() == 1,
            String.format("查找到多个升级详情，请检查taskId=%s,evseId=%s", taskId.toString(),
                evseId));

        UpgradeTaskDetailVo UpgradeTaskDetailVo = upgradeTaskDetailList.get(0);
        UpgradeTaskDetailVo.setUpdateTime(new Date());
        if (result == UpgradeEvseResultEnum.SUCCESS) {
            UpgradeTaskDetailVo.setStatus(UpdateTaskStatusEnum.UPDATED);
            UpgradeTaskDetailVo.setFailReason("");
        } else {
            UpgradeTaskDetailVo.setStatus(UpdateTaskStatusEnum.FAIL);
            UpgradeTaskDetailVo.setFailReason("网关上报失败");
        }

        if (result == UpgradeEvseResultEnum.TIMEOUT) {
            UpgradeTaskDetailVo.setFailReason("更新超时");
        }

        logger.info("更新升级详情:{}", upgradeTaskDetailRwDs.update(UpgradeTaskDetailVo));
    }

//    private void sendToMqtt(String gwno, EvseUpgradeRequest evseUpgradeRequest) {
//        mqttService.publishMessage(gwno, evseUpgradeRequest.toString());
//    }

    private void sendToMq(String gwno, EvseUpgradeRequest evseUpgradeRequest) {

        IotGwCmdCacheVo<EvseUpgradeRequest> cmd = new IotGwCmdCacheVo<>();
        cmd.setGwno(gwno)
            .setSeq(this.sequenceRwService.getNextOutRequestSeq())
            .setCmd(IotGwCmdType2.CE_UPGRADE)
            .setData(evseUpgradeRequest);  // 有部分字段冗余, 后续版本可以去掉

        logger.info("发送到MQ: {}", JsonUtils.toJsonString(cmd));
        mqService.publishMessage(gwno, JsonUtils.toJsonString(cmd));
    }
}