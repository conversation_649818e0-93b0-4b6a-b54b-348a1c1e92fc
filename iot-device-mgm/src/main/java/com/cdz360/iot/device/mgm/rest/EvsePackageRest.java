package com.cdz360.iot.device.mgm.rest;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.iot.device.mgm.ds.service.EvsePackageService;
import com.cdz360.iot.model.evse.param.ListPackageParam;
import com.cdz360.iot.model.evse.param.UpdateEvsePackageParam;
import com.cdz360.iot.model.evse.vo.EvsePackageVo;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * 海外平台升级包管理
 */
@Slf4j
@RestController
@RequestMapping("/device/evsePackage")
public class EvsePackageRest {

    @Autowired
    private EvsePackageService evsePackageService;

    @Operation(summary = "根据ID删除升级包")
    @PostMapping("/deleteById")
    public BaseResponse deleteById(@RequestBody UpdateEvsePackageParam param) {
        log.info("删除升级包. param = {}", param);
        return evsePackageService.deleteById(param);
    }

    @Operation(summary = "修改升级包状态")
    @PostMapping("/updateStatus")
    public BaseResponse updateStatus(@RequestBody UpdateEvsePackageParam param) {
        log.info("修改升级包状态. param = {}", param);
        return evsePackageService.updateStatus(param);
    }

    @Operation(summary = "获取列表")
    @PostMapping("/getList")
    public ListResponse<EvsePackageVo> getList(@RequestBody ListPackageParam param) {
        log.info("获取升级包列表. param = {}", param);
        return evsePackageService.getList(param);
    }

    @Operation(summary = "新增升级包")
    @PostMapping("/create")
    public BaseResponse create(@RequestBody UpdateEvsePackageParam param) {
        log.info("新增升级包. param = {}", param);
        return evsePackageService.createPackage(param);
    }

    @Operation(summary = "获取品牌列表")
    @GetMapping("/getBrandList")
    public ListResponse<String> getBrandList() {
        return evsePackageService.getBrandList();
    }

    @Operation(summary = "编辑升级包")
    @PostMapping("/editPackage")
    public BaseResponse editPackage(UpdateEvsePackageParam param) {
        log.info("编辑升级包. param = {}", param);
        return evsePackageService.editPackage(param);
    }

}