package com.cdz360.iot.device.mgm.rest;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.model.base.param.BaseListParam;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.iot.device.mgm.ds.service.EvseService;
import com.cdz360.iot.ds.ro.EvseBindHistoryRoDs;
import com.cdz360.iot.model.evse.EvsePo;
import com.cdz360.iot.model.evse.dto.EvseInfoDto;
import com.cdz360.iot.model.evse.dto.EvseTinyDto;
import com.cdz360.iot.model.evse.param.EvseTinyParam;
import com.cdz360.iot.model.evse.param.ListEvseCfgResultParam;
import com.cdz360.iot.model.evse.param.ListEvseParam;
import com.cdz360.iot.model.evse.po.EvseModelPo;
import com.cdz360.iot.model.evse.po.EvsePasscodePo;
import com.cdz360.iot.model.evse.vo.EvseCfgResultVo;
import com.cdz360.iot.model.evse.vo.EvseModelVo;
import com.cdz360.iot.model.param.OfflineEvseParam;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.Date;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@Tag(name = "充电桩相关接口", description = "充电桩")
@RequestMapping(value = "/device/mgm/evse", produces = MediaType.APPLICATION_JSON_VALUE)
public class EvseRest {

    @Autowired
    private EvseService evseService;

    @Autowired
    private EvseBindHistoryRoDs evseBindHistoryRoDs;


    @Operation(summary = "获取桩信息")
    @GetMapping(value = "/getEvseInfo")
    public ObjectResponse<EvseInfoDto> getEvseInfo(@RequestParam String evseNo) {
        log.info(">>获取桩信息。 evseNo = {}", evseNo);
        EvseInfoDto evse = this.evseService.getEvseInfo(evseNo);
        log.info(">>获取桩信息。 evse {}", evse);
        return RestUtils.buildObjectResponse(evse);
    }

    @Operation(summary = "获取桩列表")
    @PostMapping(value = "/getEvseInfoList")
    public ListResponse<EvseInfoDto> getEvseInfoList(@RequestBody ListEvseParam param) {
        log.info(">>获取桩列表。 param = {}", param);
        ListResponse<EvseInfoDto> res = evseService.getEvseInfoList(param);
        log.info(">>获取桩信息列表。 size = {}", res.getData().size());
        return res;
    }


    @Operation(summary = "获取桩列表")
    @PostMapping(value = "/getEvseList")
    public ListResponse<EvsePo> getEvseList(@RequestBody ListEvseParam param) {
        log.info(">>获取桩列表。 param = {}", param);
        List<EvsePo> list = evseService.getEvseList(param);
        log.info(">>获取桩信息列表。 size = {}", list.size());
        return RestUtils.buildListResponse(list);
    }

    @Operation(summary = "获取桩列表(极简版)")
    @PostMapping(value = "/getEvseTinyList")
    public ListResponse<EvseTinyDto> getEvseTinyList(@RequestBody EvseTinyParam param) {
        log.info(">>获取桩列表(极简版)。 param = {}", param);
        List<EvseTinyDto> list = evseService.getEvseTinyList(param);
        return RestUtils.buildListResponse(list);
    }

    @Operation(summary = "获取桩列表(包含品牌型号模块等信息)")
    @PostMapping(value = "/getEvseModelVoList")
    public ListResponse<EvseModelVo> getEvseModelVoList(@RequestBody ListEvseParam param) {
        log.info("getEvseModelVoList param = {}", param);
        List<EvseModelVo> list = evseService.getEvseModelVoList(param);
        log.info("getEvseModelVoList size = {}", list.size());
        return RestUtils.buildListResponse(list);
    }

    @Operation(summary = "获取设备型号")
    @GetMapping(value = "/getModelList")
    public ListResponse<EvseModelPo> getModelList(@RequestParam(value = "start") Long start,
        @RequestParam(value = "size") Integer size,
        @RequestParam(value = "keyword", required = false) String keyword) {
        log.info("getModelList start = {}, size = {}", start, size);
        return evseService.getModelList(start, size, keyword);
    }

    @Operation(summary = "获取桩可用于绑定到变压器的列表")
    @PostMapping(value = "/getEvseListForTopology")
    public ListResponse<EvsePo> getEvseListForTopology(@RequestBody ListEvseParam param) {
        log.info(">>获取变压器可用桩列表。 param = {}", param);
        List<EvsePo> list = evseService.getEvseListForTopology(param);
        log.info(">>获取变压器可用桩信息列表。 size = {}", list.size());
        return RestUtils.buildListResponse(list);
    }

    @Operation(summary = "获取桩时段内在平台的时间（秒）")
    @GetMapping(value = "/getEvseListActiveTime")
    @Deprecated
    public ObjectResponse<Long> getEvseListActiveTime(@RequestParam("startTime")
                                                      @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
                                                      @RequestParam("endTime")
                                                      @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime,
                                                      @RequestParam("evseNo") String evseNo,
                                                      @RequestParam("siteId") String siteId) {
        log.info(">>获取桩时段内在平台的时间。 param = {}, {}, {}, {}", startTime, endTime, evseNo, siteId);
        long ret = evseBindHistoryRoDs.selectActiveTime(startTime, endTime, evseNo, siteId);
        log.info(">>获取桩时段内在平台的时间: {}", ret);
        return RestUtils.buildObjectResponse(ret);
    }

    @Operation(summary = "根据桩号列表获取桩详细信息列表")
    @PostMapping(value = "/selectByEvseIds")
    public ListResponse<EvsePo> selectByEvseIds(
            @Parameter(name = "桩号列表") @RequestBody List<String> evseIds) {
        log.info(">>获取不支持分时计费桩号列表。 evseIds = {}", evseIds);
        ListResponse<EvsePo> res = evseService.selectByEvseIds(evseIds);
        log.info(">>获取桩信息列表。 size = {}", res.getData().size());
        return res;
    }


    @Operation(summary = "更新桩密钥")
    @GetMapping(value = "/updateEvsePasscode")
    public ObjectResponse<Long> updateEvsePasscode(
            @Parameter(name = "桩号") @RequestParam("evseNo") String evseNo, @RequestParam("passcode") String passcode) {
        log.info(">>更新桩密钥。 evseNo = {}，passcode = {}", evseNo, passcode);
        ObjectResponse res = evseService.updateEvsePasscode(evseNo, passcode);
        log.info(">>更新桩密钥完成。 data = {}", res.getData());
        return res;
    }

    @Operation(summary = "获取桩对应版本密钥")
    @GetMapping(value = "/getEvsePasscode")
    public ObjectResponse<EvsePasscodePo> getEvsePasscode(
            @Parameter(name = "桩号") @RequestParam("evseNo") String evseNo,
            @Parameter(name = "桩号") @RequestParam(value = "ver", required = false) Long ver) {
        log.info(">>获取桩对应版本密钥。 evseNo = {}，ver = {}", evseNo, ver);
        ObjectResponse res = evseService.getEvsePasscode(evseNo, ver);
        log.info(">>获取桩对应版本密钥完成。 data = {}", res.getData());
        return res;
    }

    @Operation(summary = "获取桩配置下发结果列表")
    @PostMapping(value = "/getEvseCfgResultList")
    public ListResponse<EvseCfgResultVo> getEvseCfgResultList(@RequestBody ListEvseCfgResultParam param) {
        log.info(">> 获取桩配置下发结果列表: param = {}", JsonUtils.toJsonString(param));
        List<EvseCfgResultVo> voList = evseService.getEvseCfgResultList(param);
        log.info("<< size = {}", voList.size());
        return RestUtils.buildListResponse(voList);
    }

    @Operation(summary = "根据条件获取不同设备类型、软件版本")
    @GetMapping(value = "/getEvseModelOrFirm")
    public ListResponse<String> getEvseModelOrFirm(@RequestParam("type") String type) {
        if (StringUtils.isBlank(type)) {
            throw new DcServiceException("类型不能为空");
        }
        List<String> voList = evseService.getEvseModelOrFirm(type);
        log.info("<< size = {}", voList.size());
        return RestUtils.buildListResponse(voList);
    }
    
    @Operation(summary = "检查脱机桩是否已存在库中")
    @PostMapping(value = "/checkOfflineEvseInDB")
    public ListResponse<OfflineEvseParam> checkOfflineEvseInDB(@RequestBody List<OfflineEvseParam> params) {
        log.info("checkOfflineEvseInDB params.size = {}", params.size());
        return evseService.checkOfflineEvseInDB(params);
    }

    @Operation(summary = "检查桩是否已存在库中")
    @PostMapping(value = "/checkEvseInDB")
    public ListResponse<OfflineEvseParam> checkEvseInDB(@RequestBody List<OfflineEvseParam> params) {
        log.info("checkEvseInDB params.size = {}", params.size());
        return evseService.checkEvseInDB(params);
    }

    @Operation(summary = "软件版本下拉框数据")
    @PostMapping(value = "/getFirmwareVerList")
    public ListResponse<String> getFirmwareVerList(@RequestBody BaseListParam param) {
        return evseService.getFirmwareVerList(param);
    }

}
