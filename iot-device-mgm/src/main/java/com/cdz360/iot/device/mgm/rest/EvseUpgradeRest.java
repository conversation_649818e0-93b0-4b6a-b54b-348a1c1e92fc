package com.cdz360.iot.device.mgm.rest;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.iot.device.mgm.ds.service.UpgradeTaskService;
import com.cdz360.iot.model.evse.dto.UpgradeTaskDto;
import com.cdz360.iot.model.evse.param.StartUpgradeTaskParam;
import com.cdz360.iot.model.evse.upgrade.UpgradeEvseResultRequest;
import com.cdz360.iot.model.evse.upgrade.UpgradeRecordVo;
import com.cdz360.iot.model.evse.upgrade.UpgradeTaskDetailRequest;
import com.cdz360.iot.model.evse.upgrade.UpgradeTaskDetailVo;
import com.cdz360.iot.model.evse.upgrade.UpgradeTaskInfoRequest;
import com.cdz360.iot.model.evse.upgrade.UpgradeTaskInfoVo;
import com.cdz360.iot.model.evse.upgrade.UpgradeTaskListRequest;
import com.cdz360.iot.model.evse.upgrade.UpgradeTaskRequest;
import com.cdz360.iot.model.evse.upgrade.UpgradeTaskVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Classname EvseUpgradeController
 * @Description TODO
 * @Date 9/18/2019 2:47 PM
 * @Created by Rafael
 */
@RestController
@Tag(name = "设备升级相关", description = "设备")
@RequestMapping("/device/upgrade")
public class EvseUpgradeRest {

    private final Logger logger = LoggerFactory.getLogger(EvseUpgradeRest.class);

    @Autowired
    private UpgradeTaskService upgradeTaskService;

    /**
     * 分页获取 获取场站下升级记录列表
     *
     * @return
     */
    @Operation(summary = "分页获取 获取场站下升级记录列表")
    @PostMapping("/getUpgradeTaskListBySite")
    public ListResponse<UpgradeTaskVo> getUpgradeTaskListBySite(
        @RequestBody UpgradeTaskListRequest updateTaskListRequest) {
        logger.info(">> 获取场站下升级记录列表: req={}", updateTaskListRequest);
        ListResponse<UpgradeTaskVo> ret = upgradeTaskService.getUpgradeTaskListBySite(
            updateTaskListRequest.getSiteId(),
            updateTaskListRequest.getTaskId(),
            updateTaskListRequest.getBundleKeyword(),
            updateTaskListRequest.getIndex(),
            updateTaskListRequest.getSize());
        logger.info("<< {}", JsonUtils.toJsonString(ret));
        return ret;
    }

    /**
     * 获取升级记录详情列表，不分页
     *
     * @return
     */
    @Operation(summary = "获取升级记录详情列表，不分页")
    @PostMapping("/getUpgradeTaskDetailListByTaskId")
    public ListResponse<UpgradeTaskDetailVo> getUpgradeTaskDetailListByTaskId(
        @RequestBody UpgradeTaskDetailRequest updateTaskDetailRequest) {
        logger.info(">> 获取升级记录详情列表: req={}",
            JsonUtils.toJsonString(updateTaskDetailRequest));
        ListResponse<UpgradeTaskDetailVo> list = upgradeTaskService
            .getUpgradeTaskDetailListByTaskId(updateTaskDetailRequest);
        logger.info("<< {}", JsonUtils.toJsonString(list));
        return list;
    }

    @Operation(summary = "获取升级信息")
    @PostMapping("/getUpgradeTaskInfo")
    public ObjectResponse<UpgradeTaskInfoVo> getUpgradeTaskInfo(
        @RequestBody UpgradeTaskInfoRequest upgradeTaskInfoRequest) {
        logger.info(">> 获取升级信息: req={}", JsonUtils.toJsonString(upgradeTaskInfoRequest));
        ObjectResponse<UpgradeTaskInfoVo> obj = upgradeTaskService
            .getUpgradeTaskInfo(upgradeTaskInfoRequest);
        logger.info("<< {}", JsonUtils.toJsonString(obj));
        return obj;
    }

    @Operation(summary = "获取桩升级记录")
    @PostMapping("/getUpgradeRecordVo")
    public ListResponse<UpgradeRecordVo> getUpgradeRecordVo(
        @RequestBody UpgradeTaskListRequest request) {
        logger.info(">> 获取桩升级记录: req={}", JsonUtils.toJsonString(request));
        return upgradeTaskService.getUpgradeRecordVo(request);
    }

    @Operation(summary = "开始升级任务")
    @PostMapping("/startUpgradeTask")
    public ObjectResponse<UpgradeTaskDto> startUpgradeTask(
        @RequestBody StartUpgradeTaskParam param) {
        logger.info("开始升级任务: {}", JsonUtils.toJsonString(param));
        return upgradeTaskService.startUpgradeTask(param);
    }

    @PostMapping("/startTask")
    public BaseResponse startTask(@RequestBody UpgradeTaskRequest upgradeTaskRequest) {
        logger.info(">> 发起桩升级: {}", JsonUtils.toJsonString(upgradeTaskRequest));
        boolean res = false;
        Assert.isTrue(
            upgradeTaskRequest.getEvseIds() != null && !upgradeTaskRequest.getEvseIds().isEmpty(),
            "请传入待升级的桩列表。");

        if (upgradeTaskRequest.getTaskId() != null && upgradeTaskRequest.getTaskId() > 0) {
            logger.info("再升级-失败的桩");
            res = upgradeTaskService.restartTask(upgradeTaskRequest.getTaskId(),
                upgradeTaskRequest.getEvseIds());

        } else {
            logger.info("全新升级桩");

            Assert.isTrue(StringUtils.isNotBlank(upgradeTaskRequest.getSiteId()), "请传入siteId");
            Assert.isTrue(upgradeTaskRequest.getBundleId() != null, "请传入升级包编号。");

            Assert.isTrue(upgradeTaskRequest.getOpId() != null, "请传入操作者id");
            Assert.isTrue(StringUtils.isNotBlank(upgradeTaskRequest.getOpName()),
                "请传入操作者name");

            res = upgradeTaskService.startTask(
                upgradeTaskRequest.getSiteId(),
                upgradeTaskRequest.getEvseIds(),
                upgradeTaskRequest.getBundleId(),
                upgradeTaskRequest.getOpId(),
                upgradeTaskRequest.getOpName()
            );

        }

        return res ? RestUtils.success() : RestUtils.serverBusy();
    }

    @PostMapping("/upgradeEvseResult")
    public BaseResponse upgradeEvseResult(@RequestBody UpgradeEvseResultRequest req) {
        logger.info("桩升级结果上报: {}", JsonUtils.toJsonString(req));
        upgradeTaskService.upgradeEvseResult(req.getEvseId(), req.getTaskNo(), req.getResult());
        return new BaseResponse();
    }
}