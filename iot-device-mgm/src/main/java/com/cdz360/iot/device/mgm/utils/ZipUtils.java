package com.cdz360.iot.device.mgm.utils;

import com.cdz360.iot.common.utils.IotAssert;
import lombok.extern.slf4j.Slf4j;
import net.lingala.zip4j.core.ZipFile;
import net.lingala.zip4j.exception.ZipException;
import net.lingala.zip4j.model.FileHeader;
import net.lingala.zip4j.progress.ProgressMonitor;
import org.apache.commons.io.ByteOrderMark;
import org.apache.commons.io.input.BOMInputStream;

import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.List;
import java.util.Optional;

@Slf4j
public class ZipUtils {
    /**
     * @param zipFilePath ZIP包路径
     * @param filePath    解压路径
     * @param password    解压密码
     * @param listener    进度监听器
     * @Description: 解压缩zip, 带解压进度
     * @Author: J<PERSON>ei
     * @CreateDate: 9:04 2019/9/18
     */
    public static void unZipWithProgress(String zipFilePath, String filePath, String password, ProgressListener listener) throws ZipException {
        ZipFile zFile = new ZipFile(zipFilePath);
        zFile.setFileNameCharset(getEncoding(zipFilePath));
        if (!zFile.isValidZipFile()) {
            throw new ZipException("ZIP压缩包无效，请检查");
        }
        File destDir = new File(filePath);
        if (destDir.isDirectory() && !destDir.exists()) {
            log.info("创建解压目录。filePath = {}", filePath);
            destDir.mkdirs();
        }
        if (zFile.isEncrypted()) {
            zFile.setPassword(password); // 设置解压密码
        }
        ProgressMonitor progressMonitor = zFile.getProgressMonitor();
        new Thread(() -> {
            try {
                if (listener == null) {
                    return;
                }
                listener.onStart();
                while (true && !progressMonitor.isCancelAllTasks()) {
                    // 每隔50ms,发送一个解压进度出去
                    Thread.sleep(50);
                    int precentDone = progressMonitor.getPercentDone();
                    if (!progressMonitor.isCancelAllTasks()) {
                        listener.onProgress(precentDone);
                    }
                    if (precentDone >= 100) {
                        break;
                    }
                }
                // 解压完成
                listener.onCompleted();
            } catch (InterruptedException e) {
                listener.onError(e);
            }
        }).start();
        // true 在子线程中进行解压 , false主线程中解压
        zFile.setRunInThread(false);
        try {
            // 将压缩文件解压到filePath中
            zFile.extractAll(filePath);
        } catch (ZipException e) {
            progressMonitor.cancelAllTasks();
            // 异常触发
            listener.onError(e);
        }
    }

    /**
     * @Description: 读取Zip文件中内容
     * @Author: JLei
     * @CreateDate: 9:06 2019/9/18
     */
    public static InputStreamReader readFromZipFile(String zipFilePath, String fileHeaderName, String password) throws ZipException, IOException {
        File zipFile = new File(zipFilePath);
        ZipFile zFile = new ZipFile(zipFile);
//        if (!zFile.isValidZipFile()) {
//            throw new ZipException("无效ZIP压缩包格式!");
//        }
        IotAssert.isTrue(zFile.isValidZipFile(), "无效ZIP压缩包格式!");
        if (zFile.isEncrypted()) {
            zFile.setPassword(password); // 设置解压密码
        }
        Optional<FileHeader> fileHeaderOpt = Optional.ofNullable(zFile.getFileHeader(fileHeaderName));
//        if (!fileHeaderOpt.isPresent()) {
//            throw new ZipException("ZIP压缩包中未获取到指定文件。");
//        }
        IotAssert.isTrue(fileHeaderOpt.isPresent(), "ZIP压缩包中未获取到指定文件。");
        //可检测多种类型，并剔除bom
        BOMInputStream bomIn = new BOMInputStream(zFile.getInputStream(fileHeaderOpt.get()), false, ByteOrderMark.UTF_8);
        String charset = "UTF-8";
        //若检测到bom，则使用bom对应的编码
        if (bomIn.hasBOM()) {
            charset = bomIn.getBOMCharsetName();
        }
        InputStreamReader reader = new InputStreamReader(bomIn, charset);
        return reader;
    }

    private static String getEncoding(String path) throws ZipException {
        String encoding = "GBK";
        ZipFile zipFile = new ZipFile(path);
        zipFile.setFileNameCharset(encoding);
        List<FileHeader> list = zipFile.getFileHeaders();
        for (int i = 0; i < list.size(); i++) {
            FileHeader fileHeader = list.get(i);
            String fileName = fileHeader.getFileName();
            if (isMessyCode(fileName)) {
                encoding = "UTF-8";
                break;
            }
        }
        return encoding;
    }

    private static boolean isMessyCode(String str) {
        for (int i = 0; i < str.length(); i++) {
            char c = str.charAt(i);
            // 当从Unicode编码向某个字符集转换时，如果在该字符集中没有对应的编码，则得到0x3f（即问号字符?）
            // 从其他字符集向Unicode编码转换时，如果这个二进制数在该字符集中没有标识任何的字符，则得到的结果是0xfffd
            if ((int) c == 0xfffd) {
                // 存在乱码
                return true;
            }
        }
        return false;
    }

//    public static void main(String[] args) {
//        try {
//            unZipWithProgress("D:\\ftpnfs\\合并软件Protocolv3.4-迪文屏v0.5-含通用语音及屏 - 副本.zip", "C:\\", "",
//                    new ZipUtils.ProgressListener() {
//                        @Override
//                        public void onStart() {
//                            System.out.println("--onStart--");
//                        }
//
//                        @Override
//                        public void onProgress(Integer progress) {
//                            System.out.println(progress);
//                        }
//
//                        @Override
//                        public void onError(Exception e) {
//                            System.out.println("--onCompleted--" + e.getMessage());
//                        }
//
//                        @Override
//                        public void onCompleted() {
//                            System.out.println("--onCompleted--");
//                        }
//                    }, true);
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }

    /**
     * 监听ZIP文件解压进度
     */
    public interface ProgressListener {
        default void onStart() {
            log.info("【开始】解压ZIP压缩包开始");
        }

        ;

        void onProgress(Integer progress);

        void onError(Exception e);

        default void onCompleted() {
            log.info("【结束】解压ZIP压缩包结束");
        }

        ;
    }
}