<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.iot.ds.rw.mapper.EvsePackageRwMapper">

  <delete id="updatePackage">
    UPDATE t_evse_package
    SET
    <if test="type != null">
      type = #{type},
    </if>
    <if test="brand != null">
      brand = #{brand},
    </if>
    <if test="status != null">
      status = #{status},
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank(packageName)">
      packageName = #{packageName},
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank(version)">
      version = #{version},
    </if>
    <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty(packageInfo)">
      packageInfo = #{packageInfo, typeHandler=com.cdz360.biz.ds.trading.ListTypeHandler},
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank(account)">
      account = #{account},
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank(password)">
      password = #{password},
    </if>
    <if test="enable != null">
      enable = false,
    </if>
    opId = #{opId},
    opName = #{opName},
    updateTime = now()
    WHERE
    id = #{id}
  </delete>

  <insert id="create" useGeneratedKeys="true" keyProperty="id"
    keyColumn="id" parameterType="com.cdz360.iot.model.evse.param.UpdateEvsePackageParam">
    insert into t_evse_package (
    `type`,
    `brand`,
    `packageName`,
    `status`,
    `version`,
    `packageInfo`,
    `account`,
    `password`,
    `opId`,
    `opName`,
    `createTime`,
    `updateTime`)
    values (#{type,typeHandler=com.cdz360.ds.type.EnumTypeHandler},
    #{brand},
    #{packageName},
    #{status},
    #{version},
    #{packageInfo, typeHandler=com.cdz360.iot.ds.ListTypeHandler},
    #{account},
    #{password},
    #{opId},
    #{opName},
    now(),
    now())
  </insert>
</mapper>