package com.cdz360.iot.ess.biz.north;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.model.base.type.IotGwCmdType2;
import com.cdz360.base.model.es.dto.ControlStrategyParamDto;
import com.cdz360.base.model.es.dto.EmsConfigParamDto;
import com.cdz360.base.model.es.dto.InOutTimeRangeDto;
import com.cdz360.base.model.es.dto.PcsConfigParamDto;
import com.cdz360.base.model.es.dto.UpdateEssCfgDto;
import com.cdz360.base.model.es.type.DevicePowerOpType;
import com.cdz360.base.model.es.type.EquipCfgStatus;
import com.cdz360.base.model.es.type.EssCfgStrategy;
import com.cdz360.base.model.es.type.EssConfigType;
import com.cdz360.base.model.es.type.InverterGridMode;
import com.cdz360.base.model.es.vo.EssCfgVo;
import com.cdz360.base.model.es.vo.EssInOutStrategyDto;
import com.cdz360.base.model.es.vo.EssPriceDto;
import com.cdz360.base.model.iot.type.EquipStatus;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.data.sync.model.iot.IotGwDownCmd;
import com.cdz360.data.sync.service.DcEventPublisher;
import com.cdz360.iot.biz.MqService;
import com.cdz360.iot.common.base.IotConstants;
import com.cdz360.iot.common.utils.FeignResponseValidate;
import com.cdz360.iot.common.utils.IotAssert;
import com.cdz360.iot.ds.ro.GwInfoRoDs;
import com.cdz360.iot.ds.ro.UpgradeLogRoDs;
import com.cdz360.iot.ds.ro.ess.ds.EssCfgRoDs;
import com.cdz360.iot.ds.ro.ess.ds.EssDtuRoDs;
import com.cdz360.iot.ds.ro.ess.ds.EssEquipRoDs;
import com.cdz360.iot.ds.ro.ess.ds.EssRoDs;
import com.cdz360.iot.ds.ro.ess.ds.PcsRoDs;
import com.cdz360.iot.ds.rw.EssCfgRwDs;
import com.cdz360.iot.ds.rw.EssRwDs;
import com.cdz360.iot.ds.rw.SequenceRwService;
import com.cdz360.iot.ess.biz.EssCfgService;
import com.cdz360.iot.ess.biz.south.EssMqService;
import com.cdz360.iot.ess.feign.DeviceMgmFeignClient;
import com.cdz360.iot.ess.model.param.EssDynamicCfgRWParam;
import com.cdz360.iot.model.dtu.po.EssDtuPo;
import com.cdz360.iot.model.modbus.dto.ModbusDecimalTv;
import com.cdz360.iot.model.ess.mqtt.ClearAlarmReq;
import com.cdz360.iot.model.ess.mqtt.DeliverUserEssCfgReq;
import com.cdz360.iot.model.ess.mqtt.EssSoftStartReq;
import com.cdz360.iot.model.ess.mqtt.EssUpgradeReq;
import com.cdz360.iot.model.ess.mqtt.EssUpgradeReq.FetchPgType;
import com.cdz360.iot.model.ess.mqtt.GetEssCfgReq;
import com.cdz360.iot.model.ess.mqtt.PgItemInfo;
import com.cdz360.iot.model.ess.mqtt.PowerOffOrOnReq;
import com.cdz360.iot.model.ess.po.EssCfgPo;
import com.cdz360.iot.model.ess.po.EssChargeStrategyPo;
import com.cdz360.iot.model.ess.po.EssEquipPo;
import com.cdz360.iot.model.ess.po.EssPo;
import com.cdz360.iot.model.ess.type.EssBootMode;
import com.cdz360.iot.model.ess.type.EssSwitchCommand;
import com.cdz360.iot.model.evse.param.StartUpgradeTaskParam;
import com.cdz360.iot.model.gw.po.UpgradeLogPo;
import com.cdz360.iot.model.gw.vo.UpgradeLogVo;
import com.cdz360.iot.model.modbus.type.ModbusDataType;
import com.cdz360.iot.model.pcs.po.PcsPo;
import com.cdz360.iot.model.site.dto.GwInfoDto;
import com.cdz360.iot.model.site.po.GwInfoPo;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectNode;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.ZoneId;
import java.util.List;
import java.util.Locale;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.slf4j.event.Level;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class EssNorthBizService {

    // 下发指令超时时间: 单位: 秒
    @Value("${iot.cmd.timeout:30}")
    private Integer timeout;

    @Autowired
    private EssEquipRoDs essEquipRoDs;

    @Autowired
    private DcEventPublisher dcEventPublish;

    @Autowired
    private MqService mqService;

    @Autowired
    private SequenceRwService sequenceRwService;

    @Autowired
    private EssCfgService essCfgService;

    @Autowired
    private EssMqService essMqService;

    @Autowired
    private UpgradeLogRoDs upgradeLogRoDs;

    @Autowired
    private GwInfoRoDs gwInfoRoDs;

    @Autowired
    private PcsRoDs pcsRoDs;

    @Autowired
    private EssCfgRoDs essCfgRoDs;

    @Autowired
    private EssCfgRwDs essCfgRwDs;

    @Autowired
    private EssDtuRoDs essDtuRoDs;

    @Autowired
    private EssRwDs essRwDs;

    @Autowired
    private EssRoDs essRoDs;

    @Autowired
    private DeviceMgmFeignClient deviceMgmFeignClient;

    @Transactional(readOnly = true)
    public EssPriceDto getPriceCfg(String essDno) {
        if (StringUtils.isBlank(essDno)) {
            throw new DcArgumentException("EMS 设备号无效");
        }
        EssPo emu = essRoDs.getByDno(essDno);
        if (emu == null) {
            log.warn("EMS 设备号无效,设备信息不存在");
            throw new DcArgumentException("EMS 设备号无效");
        }
        return essCfgService.getPriceCfg(essDno, emu.getCfgId());
    }

    public Mono<ObjectResponse<String>> getEssCfgInTime(String essDno, List<EssConfigType> types) {
        if (StringUtils.isBlank(essDno)) {
            throw new DcArgumentException("EMS 设备号无效");
        }

        EssPo ess = essRoDs.getByDno(essDno);
        if (ess == null) {
            log.warn("EMS 设备号无效,设备信息不存在");
            throw new DcArgumentException("DTU 设备号无效");
        }

        if (List.of(EquipStatus.UNKNOWN, EquipStatus.OFFLINE, EquipStatus.OFF)
            .contains(ess.getStatus())) {
            log.warn("EMS 不在线,请确认设备状态是否有效");
            throw new DcArgumentException("ems.offline");
        }

        GwInfoDto gwInfo = gwInfoRoDs.getByGwno(ess.getGwno());
        IotAssert.isNotNull(gwInfo, "关联网关不存在");

        String deliverSeq = this.sequenceRwService.getNextOutRequestSeq();
        if (gwInfo.getVer() == IotConstants.IOT_GW_VER_1) {
            throw new DcServiceException("废弃的逻辑", Level.ERROR);
        } else if (gwInfo.getVer() == IotConstants.IOT_GW_VER_2
            || gwInfo.getVer() == IotConstants.IOT_GW_VER_3) {
            GetEssCfgReq.builder builder = new GetEssCfgReq.builder(ess.getGwno(), deliverSeq);
            GetEssCfgReq.REQ req = builder.types(types).build();
            mqService.publishMessage(gwInfo, false, JsonUtils.toJsonString(req));
        } else {
            log.error("网关版本无效: {}，无法下发获取ESS配置指令。", ess.getGwno());
        }

        return Mono.just(RestUtils.buildObjectResponse(deliverSeq));
    }

    public Mono<BaseResponse> getUserEssCfgInTime(String essDno) {
        if (StringUtils.isBlank(essDno)) {
            throw new DcArgumentException("EMS 设备号无效");
        }

        EssPo ess = essRoDs.getByDno(essDno);
        if (ess == null) {
            log.warn("EMS 设备号无效,设备信息不存在");
            throw new DcArgumentException("DTU 设备号无效");
        }

        if (List.of(EquipStatus.UNKNOWN, EquipStatus.OFFLINE, EquipStatus.OFF)
            .contains(ess.getStatus())) {
            log.warn("EMS 不在线,请确认设备状态是否有效");
            throw new DcArgumentException("ems.offline");
        }

        EssDtuPo dtu = essDtuRoDs.getByEssDno(essDno);
        if (dtu == null) {
            log.warn("DTU 设备号无效,设备信息不存在");
            throw new DcArgumentException("DTU 设备号无效");
        }

        GwInfoDto gwInfo = gwInfoRoDs.getByGwno(dtu.getGwno());
        IotAssert.isNotNull(gwInfo, "关联网关不存在");

        String deliverSeq = this.sequenceRwService.getNextOutRequestSeq();
        if (gwInfo.getVer() == IotConstants.IOT_GW_VER_1) {
            throw new DcServiceException("废弃的逻辑", Level.ERROR);
        } else if (gwInfo.getVer() == IotConstants.IOT_GW_VER_2
            || gwInfo.getVer() == IotConstants.IOT_GW_VER_3) {
            GetEssCfgReq.builder builder = new GetEssCfgReq.builder(dtu.getGwno(), deliverSeq);
            builder.serialNo(dtu.getSerialNo())
                .types(List.of(
                    EssConfigType.CHARGING_STRATEGY, EssConfigType.DISCHARGING_STRATEGY));
            GetEssCfgReq.REQ req = builder.build();
            mqService.publishMessage(gwInfo, false, JsonUtils.toJsonString(req));
        } else {
            log.error("网关版本无效: {}，无法下发获取ESS配置指令。", dtu.getGwno());
        }

        return Mono.just(RestUtils.success());
    }

    @Transactional(readOnly = true)
    public EssCfgVo getEssConfig(String essDno) {
        if (StringUtils.isBlank(essDno)) {
            throw new DcArgumentException("EMS 设备号无效");
        }
        EssPo emu = essRoDs.getByDno(essDno);
        if (emu == null) {
            log.warn("EMS 设备号无效,设备信息不存在");
            throw new DcArgumentException("EMS 设备号无效");
        }

        EssCfgVo result = new EssCfgVo();
        result.setDno(essDno)
            .setCfgId(emu.getCfgId())
            .setDeliverCfgId(emu.getDeliverCfgId())
            .setCfgStatus(emu.getCfgStatus());

        if (emu.getCfgId() != null) {
            EssCfgPo cfg = essCfgRoDs.getByCfgId(emu.getCfgId());
            if (null != cfg) {
                UpdateEssCfgDto activeCfg = new UpdateEssCfgDto();
                activeCfg.setEmsConfigParam(new EmsConfigParamDto()
                    .setUploadDeviceInfoDuration(cfg.getUploadInfoTime())
                    .setUploadRtDataDuration(cfg.getUploadDataTime()));
                if (null != cfg.getPriceCfg() &&
                    CollectionUtils.isNotEmpty(cfg.getPriceCfg().getItems())) {
                    activeCfg.setChargePriceItems(cfg.getPriceCfg().getItems());
                }
                if (null != cfg.getDischargePriceCfg() &&
                    CollectionUtils.isNotEmpty(cfg.getDischargePriceCfg().getItems())) {
                    activeCfg.setDischargePriceItems(cfg.getDischargePriceCfg().getItems());
                }
                if (null != cfg.getChargeStrategy() &&
                    CollectionUtils.isNotEmpty(cfg.getChargeStrategy().getItems())) {
                    activeCfg.setInOutItems(cfg.getChargeStrategy().getItems());
                }
                if (null != cfg.getStrategyCfg()) {
                    ControlStrategyParamDto ctrlParam = new ControlStrategyParamDto();
                    BeanUtils.copyProperties(cfg.getStrategyCfg(), ctrlParam);
                    activeCfg.setControlStrategyParam(ctrlParam);
                }
                result.setActiveCfg(activeCfg);
            }
        }

        // PCS配置参数
        List<PcsPo> pcsList = this.pcsRoDs.getPcsListByEssDno(emu.getDno());
        if (CollectionUtils.isNotEmpty(pcsList)) {
            PcsPo pcs = pcsList.get(0); // 默认只有一个

            PcsConfigParamDto pcsCfg = new PcsConfigParamDto();
            if (null != pcs.getGridMode()) {
                switch (pcs.getGridMode()) {
                    case INIT:
                        pcsCfg.setInverterRuntimeMode(InverterGridMode.INIT);
                        break;
                    case OFF_GRID:
                        pcsCfg.setInverterRuntimeMode(InverterGridMode.OFF_GRID);
                        break;
                    case ON_GRID:
                        pcsCfg.setInverterRuntimeMode(InverterGridMode.PARALLEL_GRID);
                        break;
                }
            }

            pcsCfg.setPcsRatedPower(pcs.getRatedPower())
                .setSyncSysTime2Pcs(pcs.getSyncSysTime())
                .setInverterActivePower(pcs.getInvPg())
                .setInverterReactivePower(pcs.getInvQg());

            if (null == result.getActiveCfg()) {
                result.setActiveCfg(new UpdateEssCfgDto());
            }

            result.getActiveCfg().setPcsConfigParam(pcsCfg);
        }

        // PCS额定功率
        if (null != result.getActiveCfg() &&
            null != result.getActiveCfg().getPcsConfigParam() &&
            null != result.getActiveCfg().getControlStrategyParam()) {
            BigDecimal pcsPn = result.getActiveCfg().getPcsConfigParam().getPcsRatedPower();
            result.getActiveCfg().getControlStrategyParam().setPcsPn(pcsPn);
        }

        return result;
    }

    @Transactional(readOnly = true)
    public EssCfgVo getEssSetting(String essDno) {
        if (StringUtils.isBlank(essDno)) {
            throw new DcArgumentException("EMS 设备号无效");
        }
        EssPo emu = essRoDs.getByDno(essDno);
        if (emu == null) {
            log.warn("EMS 设备号无效,设备信息不存在");
            throw new DcArgumentException("EMS 设备号无效");
        }

        EssCfgVo result = new EssCfgVo();
        result.setDno(essDno)
            .setCfgId(emu.getCfgId())
            .setDeliverCfgId(emu.getDeliverCfgId())
            .setCfgStatus(emu.getCfgStatus());
        if (emu.getCfgId() != null) {
            EssCfgPo cfg = essCfgRoDs.getByCfgId(emu.getCfgId());
            if (null != cfg) {
                if (cfg.getStrategy() != null) {
                    result.setStrategy(cfg.getStrategy());
                }

                if (cfg.getChargeStrategy() != null) {
                    result.setInOutItems(cfg.getChargeStrategy().getItems());
                }

                result.setOtherStrategy(cfg.getOtherStrategy())
                    .setRepeatCycle(cfg.getRepeatCycle())
                    .setEffectiveStartTime(cfg.getEffectiveStartTime())
                    .setEffectiveEndTime(cfg.getEffectiveEndTime());

                result.setSupportDivision(false); // 当前固定不支持，后续根据实际调整
            }

            if (null != emu.getDeliverCfgId() && emu.getDeliverCfgId() > 0) {
                EssCfgPo deliverCfg = essCfgRoDs.getByCfgId(emu.getDeliverCfgId());
                if (null != deliverCfg) {
                    result.setDeliverStrategy(deliverCfg.getStrategy())
                        .setDeliverOtherStrategy(deliverCfg.getOtherStrategy())
                        .setDeliverRepeatCycle(deliverCfg.getRepeatCycle())
                        .setDeliverEffectiveStartTime(deliverCfg.getEffectiveStartTime())
                        .setDeliverEffectiveEndTime(deliverCfg.getEffectiveEndTime())
                        .setDeliverCreateTime(
                            Instant.ofEpochMilli(deliverCfg.getCreateTime().getTime())
                                .atZone(ZoneId.systemDefault())
                                .toLocalDateTime());
                    if (deliverCfg.getChargeStrategy() != null) {
                        result.setDeliverInOutItems(deliverCfg.getChargeStrategy().getItems());
                    }
                }
            }
        }
        return result;
    }

    public Mono<String> deviceUpdateSetting(String dno, UpdateEssCfgDto dto) {
        IotAssert.isNotBlank(dno, "ess.dno.invalid");
        final EssPo ess = essRoDs.getByDno(dno);
        IotAssert.isNotNull(ess, "ess.dno.invalid");

        if (List.of(EquipStatus.UNKNOWN, EquipStatus.OFFLINE, EquipStatus.OFF)
            .contains(ess.getStatus())) {
            log.warn("设备不在线,请确认设备状态是否有效");
            throw new DcArgumentException("设备不在线，请确认设备状态是否有效");
        }

        this.sendUpdateEssCfgCmd(ess.getGwno(), essCfgService.updateEssCfg(ess, dto));
        return Mono.just(ess.getDno());
    }

    public Mono<String> deviceRefreshSetting(InOutTimeRangeDto dto) {
        IotAssert.isNotBlank(dto.getDno(), "ess.dno.invalid");

        final EssPo ess = essRoDs.getByDno(dto.getDno()); // 户储ESS和通讯设备是1:1关系
        IotAssert.isNotNull(ess, "ess.dno.invalid");

        if (List.of(EquipStatus.UNKNOWN, EquipStatus.OFFLINE, EquipStatus.OFF)
            .contains(ess.getStatus())) {
            log.warn("设备不在线,请确认设备状态是否有效");
            throw new DcArgumentException("设备不在线，请确认设备状态是否有效");
        }

        if (EssCfgStrategy.TIMING_CHARGING_DISCHARGING.equals(dto.getStrategy()) &&
            CollectionUtils.isEmpty(dto.getInOutItems())) {
            if (null != dto.getOtherStrategy() ||
                null != dto.getRepeatCycle() ||
                null != dto.getEffectiveStartTime() ||
                null != dto.getEffectiveEndTime()) {
                // nothing to do
            } else {
                log.warn("参数错误,计费时段不能为空. dno = {}, dto = {}", dto.getDno(), dto);
                throw new DcServiceException("参数错误,计费时段不能为空");
            }
        }

//        if (CollectionUtils.isNotEmpty(dto.getInOutItems())) {
//            int lastEnd = 0;
//            for (var item : dto.getInOutItems()) {
//                if (lastEnd != item.getStart()) {
//                    log.warn("参数错误,分段的时间不连续. dno = {}, dto = {}", dto.getDno(), dto);
//                    throw new DcServiceException("参数错误,分段的时间不连续");
//                }
//                lastEnd = item.getEnd();
//            }
//            if (lastEnd != 1440) {
//                log.warn("参数错误,分段结束时间不是1440. dno = {}, dto = {}", dto.getDno(), dto);
//                throw new DcServiceException("参数错误,分段结束时间不合法");
//            }
//        }

        Long cfgId = ess.getCfgId();
        if (null == cfgId || cfgId == 0) {
            EssCfgPo newCfg = new EssCfgPo();
            newCfg.setCfgNo(RandomStringUtils.randomAlphabetic(8).toUpperCase(Locale.US))
                .setBootMode(EssBootMode.UNKNOWN)
                .setSwitchCommand(EssSwitchCommand.UNKNOWN)
                .setStrategy(dto.getStrategy())
                .setOtherStrategy(dto.getOtherStrategy())
                .setRepeatCycle(dto.getRepeatCycle())
                .setEffectiveStartTime(dto.getEffectiveStartTime())
                .setEffectiveEndTime(dto.getEffectiveEndTime())
                .setChargeStrategy(new EssChargeStrategyPo()
                    .setName("")
                    .setItems(dto.getInOutItems()));
            essCfgRwDs.insertEssCfg(newCfg);

            cfgId = newCfg.getCfgId();
        } else {
            EssCfgPo oldCfg = essCfgRoDs.getByCfgId(cfgId);
            if (oldCfg == null) {
                oldCfg = new EssCfgPo();
                oldCfg.setCfgNo(RandomStringUtils.randomAlphabetic(8).toUpperCase(Locale.US));
            }
            oldCfg.setStrategy(dto.getStrategy());

            if (null != dto.getOtherStrategy()) {
                oldCfg.setOtherStrategy(dto.getOtherStrategy());
            }

            if (null != dto.getRepeatCycle()) {
                oldCfg.setRepeatCycle(dto.getRepeatCycle());
            }

            if (null != dto.getEffectiveStartTime()) {
                oldCfg.setEffectiveStartTime(dto.getEffectiveStartTime());
            }

            if (null != dto.getEffectiveEndTime()) {
                oldCfg.setEffectiveEndTime(dto.getEffectiveEndTime());
            }

            if (CollectionUtils.isNotEmpty(dto.getInOutItems())) {
                oldCfg.setChargeStrategy(new EssChargeStrategyPo()
                    .setName("")
                    .setItems(dto.getInOutItems()));
            }

            essCfgRwDs.insertEssCfg(oldCfg);
            if (oldCfg.getCfgId() == null) {
                log.warn("记录储能充放电失败.dno = {}, dto = {}", dto.getDno(), dto);
                throw new DcServiceException("记录储能充放电失败");
            }
            cfgId = oldCfg.getCfgId();
        }

        // 取最新配置ID
        EssPo emu4Update = new EssPo();
        emu4Update.setDno(dto.getDno())
            .setDeliverCfgId(cfgId)
            .setCfgStatus(EquipCfgStatus.SEND_2_GW);
        essRwDs.updateEss(emu4Update);

        // MQ推送
        EssDtuPo dtu = essDtuRoDs.getByEssDno(ess.getDno()); // 户储ESS和通讯设备是1:1关系
        if (dtu != null) {
            GwInfoDto gwInfo = gwInfoRoDs.getByGwno(dtu.getGwno());
            if (gwInfo != null) {
                if (gwInfo.getVer() == IotConstants.IOT_GW_VER_1) {
                    throw new DcServiceException("废弃的逻辑", Level.ERROR);
                } else if (gwInfo.getVer() == IotConstants.IOT_GW_VER_2
                    || gwInfo.getVer() == IotConstants.IOT_GW_VER_3) {
                    DeliverUserEssCfgReq.builder builder = new DeliverUserEssCfgReq.builder(
                        dtu.getGwno(), this.sequenceRwService.getNextOutRequestSeq());
                    builder.setCfgId(cfgId)
                        .setDno(dto.getDno())
                        .setSerialNo(dtu.getSerialNo())
                        .strategy(dto.getStrategy())
                        .otherStrategy(dto.getOtherStrategy())
                        .repeatCycle(dto.getRepeatCycle())
                        .effectiveStartTime(dto.getEffectiveStartTime())
                        .effectiveEndTime(dto.getEffectiveEndTime())
                        .supportDivision(false) // 默认不分
                        .inOutItems(dto.getInOutItems());
                    DeliverUserEssCfgReq.REQ req = builder.build();
                    mqService.publishMessage(gwInfo, false, JsonUtils.toJsonString(req));

                    IotGwDownCmd downCmd = new IotGwDownCmd();
                    downCmd.setTtl(timeout)
                        .setMsg(JsonUtils.toJsonString(req.getData()))
                        .setCmd(IotGwCmdType2.EMU_D_MODIFY_CFG)
                        .setGwno(dtu.getGwno())
                        .setSeq(dtu.getSerialNo())
                        .setVer(gwInfo.getVer());

                    this.dcEventPublish.publishIotGwDownCmd(downCmd);
                } else {
                    log.error("网关版本无效: {}，无法下发获取ESS配置指令。", dtu.getGwno());
                }
            }
        }

        return Mono.just(dto.getDno());
    }

    public Mono<String> deviceNotifySetting(InOutTimeRangeDto dto) {
        IotAssert.isNotBlank(dto.getDno(), "ess.dno.invalid");

        final EssPo ess = essRoDs.getByDno(dto.getDno()); // 户储ESS和通讯设备是1:1关系
        IotAssert.isNotNull(ess, "ess.dno.invalid");

        if (EssCfgStrategy.TIMING_CHARGING_DISCHARGING.equals(dto.getStrategy()) &&
            CollectionUtils.isEmpty(dto.getInOutItems())) {
            if (null != dto.getOtherStrategy() ||
                null != dto.getRepeatCycle() ||
                null != dto.getEffectiveStartTime() ||
                null != dto.getEffectiveEndTime()) {
                // nothing to do
            } else {
                log.warn("参数错误,计费时段不能为空. dno = {}, dto = {}", dto.getDno(), dto);
                throw new DcServiceException("参数错误,计费时段不能为空");
            }
        }

        Long cfgId = ess.getCfgId();
        if (null == cfgId || cfgId == 0) {
            EssCfgPo newCfg = new EssCfgPo();
            newCfg.setCfgNo(RandomStringUtils.randomAlphabetic(8).toUpperCase(Locale.US))
                .setBootMode(EssBootMode.UNKNOWN)
                .setSwitchCommand(EssSwitchCommand.UNKNOWN)
                .setStrategy(dto.getStrategy())
                .setOtherStrategy(dto.getOtherStrategy())
                .setRepeatCycle(dto.getRepeatCycle())
                .setEffectiveStartTime(dto.getEffectiveStartTime())
                .setEffectiveEndTime(dto.getEffectiveEndTime())
                .setChargeStrategy(new EssChargeStrategyPo()
                    .setName("")
                    .setItems(dto.getInOutItems()));

            if (null == newCfg.getStrategy()) {
                newCfg.setStrategy(EssCfgStrategy.UNKNOWN);
            }
            essCfgRwDs.insertEssCfg(newCfg);

            cfgId = newCfg.getCfgId();
        } else {
            EssCfgPo oldCfg = essCfgRoDs.getByCfgId(cfgId);
            if (oldCfg == null) {
                oldCfg = new EssCfgPo();
                oldCfg.setCfgNo(RandomStringUtils.randomAlphabetic(8).toUpperCase(Locale.US));
            }
            if (null != dto.getStrategy()) {
                oldCfg.setStrategy(dto.getStrategy());
            }

            if (null != dto.getOtherStrategy()) {
                oldCfg.setOtherStrategy(dto.getOtherStrategy());
            }

            if (null != dto.getRepeatCycle()) {
                oldCfg.setRepeatCycle(dto.getRepeatCycle());
            }

            if (null != dto.getEffectiveStartTime()) {
                oldCfg.setEffectiveStartTime(dto.getEffectiveStartTime());
            }

            if (null != dto.getEffectiveEndTime()) {
                oldCfg.setEffectiveEndTime(dto.getEffectiveEndTime());
            }

            if (CollectionUtils.isNotEmpty(dto.getInOutItems())) {
                oldCfg.setChargeStrategy(new EssChargeStrategyPo()
                    .setName("")
                    .setItems(dto.getInOutItems()));
            }

            essCfgRwDs.insertEssCfg(oldCfg);
            if (oldCfg.getCfgId() == null) {
                log.warn("记录储能充放电失败.dno = {}, dto = {}", dto.getDno(), dto);
                throw new DcServiceException("记录储能充放电失败");
            }
            cfgId = oldCfg.getCfgId();
        }

        // 取最新配置ID
        EssPo emu4Update = new EssPo();
        emu4Update.setDno(dto.getDno())
            .setCfgId(cfgId);
        boolean b = essRwDs.updateEss(emu4Update);
        log.info("APP配置更新通知: {} / {}", b, cfgId);

        return Mono.just(dto.getDno());
    }

    @Transactional(readOnly = false)
    public EssCfgPo configPriceCfg(String essDno, EssPriceDto priceCfgIn) {
        if (StringUtils.isBlank(essDno)) {
            throw new DcArgumentException("EMS 设备号无效");
        }
        EssPo emu = essRoDs.getByDno(essDno);
        if (emu == null) {
            log.warn("EMS 设备号无效,设备信息不存在");
            throw new DcArgumentException("EMS 设备号无效");
        }
        return this.essCfgService.configPriceCfg(emu, priceCfgIn);
    }


    /**
     * 发送更新配置的下行指令，通知EMS去云端获取新的配置
     */
    public void sendUpdateEssCfgCmd(String gwno, EssCfgPo cfg) {
        GwInfoPo gwInfo = this.gwInfoRoDs.getByGwno(gwno);
        essMqService.sendUpdateEssCfgCmd(gwInfo, cfg.getCfgId(), cfg.getCfgNo());

        // FIXME: 下发超时监控
    }

    /**
     * 发送更新配置的下行指令，通知EMS去云端获取新的配置
     */
    public void configEquipCfg(String equipDno, JsonNode cfg) {
        EssEquipPo equip = essEquipRoDs.getByDno(equipDno);
        IotAssert.isNotNull(equip, "设备编号无效");

        GwInfoPo gwInfo = this.gwInfoRoDs.getByGwno(equip.getEssDno());
        // 为每个元素添加类型标识，Jackson 反序列化 ModbusAbstractTv 子类时需要 't' 字段
        if (cfg.isArray()) {
            for (JsonNode node : cfg) {
                if (node.isObject()) {
                    ((ObjectNode) node).put("t", ModbusDataType.DECIMAL.getCode());
                }
            }
        }

        List<ModbusDecimalTv> tvs = JsonUtils.fromJson(cfg,
            new TypeReference<List<ModbusDecimalTv>>() {
            });
        essMqService.sendUpdateEssEquipCfgCmd(
            gwInfo, equip.getDno(), equip.getEquipType(), tvs);

        // FIXME: 下发超时监控
    }

    @Transactional(readOnly = true)
    public EssInOutStrategyDto getChargeStrategy(String essDno) {
        if (StringUtils.isBlank(essDno)) {
            throw new DcArgumentException("EMS 设备号无效");
        }
        EssPo emu = essRoDs.getByDno(essDno);
        if (emu == null) {
            log.warn("EMS 设备号无效,设备信息不存在");
            throw new DcArgumentException("EMS 设备号无效");
        }
        return essCfgService.getChargeStrategyDto(essDno, emu.getCfgId());
    }


    @Transactional(readOnly = false)
    public EssCfgPo configChargeStrategy(String essDno, EssInOutStrategyDto strategyIn) {
        if (StringUtils.isBlank(essDno)) {
            throw new DcArgumentException("EMS 设备号无效");
        }
        EssPo emu = essRoDs.getByDno(essDno);
        if (emu == null) {
            log.warn("EMS 设备号无效,设备信息不存在");
            throw new DcArgumentException("EMS 设备号无效");
        }
        return this.essCfgService.configChargeStrategy(emu, strategyIn);
    }

    public Mono<String> deviceFaultClear(String dno) {
        IotAssert.isNotBlank(dno, "ess.dno.invalid");

        final EssPo ess = essRoDs.getByDno(dno); // 户储ESS和通讯设备是1:1关系
        IotAssert.isNotNull(ess, "ess.dno.invalid");

        if (List.of(EquipStatus.UNKNOWN, EquipStatus.OFFLINE, EquipStatus.OFF)
            .contains(ess.getStatus())) {
            log.warn("设备不在线,请确认设备状态是否有效");
            throw new DcArgumentException("设备不在线，请确认设备状态是否有效");
        }

        // MQ推送
        EssDtuPo dtu = essDtuRoDs.getByEssDno(ess.getDno());
        if (dtu != null) {
            GwInfoDto gwInfo = gwInfoRoDs.getByGwno(dtu.getGwno());
            if (gwInfo != null) {
                if (gwInfo.getVer() == IotConstants.IOT_GW_VER_1) {
                    throw new DcServiceException("废弃的逻辑", Level.ERROR);
                } else if (gwInfo.getVer() == IotConstants.IOT_GW_VER_2
                    || gwInfo.getVer() == IotConstants.IOT_GW_VER_3) {
                    ClearAlarmReq.builder builder = new ClearAlarmReq.builder(
                        dtu.getGwno(), this.sequenceRwService.getNextOutRequestSeq());
                    builder.setSerialNo(dtu.getSerialNo());
                    ClearAlarmReq.REQ req = builder.build();
                    mqService.publishMessage(gwInfo, false, JsonUtils.toJsonString(req));
                } else {
                    log.error("网关版本无效: {}，无法下发获取ESS配置指令。", dtu.getGwno());
                }
            }
        }

        return Mono.just(dno);
    }

    public Mono<String> devicePowerOp(String dno, Boolean on) {
        IotAssert.isNotBlank(dno, "ess.dno.invalid");

        final EssPo ess = essRoDs.getByDno(dno); // 户储ESS和通讯设备是1:1关系
        IotAssert.isNotNull(ess, "ess.dno.invalid");

        if (List.of(EquipStatus.UNKNOWN, EquipStatus.OFFLINE, EquipStatus.OFF)
            .contains(ess.getStatus())) {
            log.warn("设备不在线,请确认设备状态是否有效");
            throw new DcArgumentException("设备不在线，请确认设备状态是否有效");
        }

        // MQ推送
        EssDtuPo dtu = essDtuRoDs.getByEssDno(ess.getDno());
        if (dtu != null) {
            GwInfoDto gwInfo = gwInfoRoDs.getByGwno(dtu.getGwno());
            if (gwInfo != null) {
                if (gwInfo.getVer() == IotConstants.IOT_GW_VER_1) {
                    throw new DcServiceException("废弃的逻辑", Level.ERROR);
                } else if (gwInfo.getVer() == IotConstants.IOT_GW_VER_2
                    || gwInfo.getVer() == IotConstants.IOT_GW_VER_3) {
                    PowerOffOrOnReq.builder builder = new PowerOffOrOnReq.builder(
                        dtu.getGwno(), this.sequenceRwService.getNextOutRequestSeq());
                    builder.serialNo(dtu.getSerialNo()).targetInv().powerOn(on);
                    PowerOffOrOnReq.REQ req = builder.build();
                    mqService.publishMessage(gwInfo, false, JsonUtils.toJsonString(req));
                } else {
                    log.error("网关版本无效: {}，无法下发获取ESS配置指令。", dtu.getGwno());
                }
            }
        }

        return Mono.just(dno);
    }

    public Mono<String> devicePcsPowerOp(String dno, DevicePowerOpType powerOp) {
        IotAssert.isNotBlank(dno, "ess.dno.invalid");

        final EssPo ess = essRoDs.getByDno(dno);
        IotAssert.isNotNull(ess, "ess.dno.invalid");

        if (List.of(EquipStatus.UNKNOWN, EquipStatus.OFFLINE, EquipStatus.OFF)
            .contains(ess.getStatus())) {
            log.warn("设备不在线,请确认设备状态是否有效");
            throw new DcArgumentException("设备不在线，请确认设备状态是否有效");
        }

        GwInfoDto gwInfo = gwInfoRoDs.getByGwno(ess.getGwno());
        if (gwInfo != null) {
            if (gwInfo.getVer() == IotConstants.IOT_GW_VER_1) {
                throw new DcServiceException("废弃的逻辑", Level.ERROR);
            } else if (gwInfo.getVer() == IotConstants.IOT_GW_VER_2
                || gwInfo.getVer() == IotConstants.IOT_GW_VER_3) {
                PowerOffOrOnReq.builder builder = new PowerOffOrOnReq.builder(
                    ess.getGwno(), this.sequenceRwService.getNextOutRequestSeq());
                builder.targetPcs().powerOp(powerOp);
                PowerOffOrOnReq.REQ req = builder.build();
                mqService.publishMessage(gwInfo, false, JsonUtils.toJsonString(req));
            } else {
                log.error("网关版本无效: {}，无法下发获取ESS配置指令。", ess.getGwno());
            }
        }

        return Mono.just(dno);
    }

    public Mono<Object> deviceUpgradeOp(StartUpgradeTaskParam param) {

        return deviceMgmFeignClient.startUpgradeTask(param)
            .doOnNext(FeignResponseValidate::check)
            .map(ObjectResponse::getData)
            .map(upgradeData -> {
                final EssPo ess = essRoDs.getByDno(param.getEssDno());
                IotAssert.isNotNull(ess, "ess.dno.invalid");

                if (List.of(EquipStatus.UNKNOWN, EquipStatus.OFFLINE, EquipStatus.OFF)
                    .contains(ess.getStatus())) {
                    log.warn("设备不在线,请确认设备状态是否有效");
                    throw new DcArgumentException("设备不在线，请确认设备状态是否有效");
                }

                EssDtuPo dtu = essDtuRoDs.getByEssDno(ess.getDno());
                if (dtu != null) {
                    GwInfoDto gwInfo = gwInfoRoDs.getByGwno(ess.getGwno());
                    if (gwInfo != null) {
                        if (gwInfo.getVer() == IotConstants.IOT_GW_VER_1) {
                            throw new DcServiceException("废弃的逻辑", Level.ERROR);
                        } else if (gwInfo.getVer() == IotConstants.IOT_GW_VER_2
                            || gwInfo.getVer() == IotConstants.IOT_GW_VER_3) {
                            EssUpgradeReq.builder builder = new EssUpgradeReq.builder(
                                ess.getGwno(), this.sequenceRwService.getNextOutRequestSeq(),
                                ess.getDno());
                            builder.serialNo(dtu.getSerialNo())
                                .pgId(param.getUpgradePgId())
                                .upgradeId(upgradeData.getUpgradeLogId())
                                .fetchPgType(FetchPgType.NFS)
//                                .fetchAcc(upgradeData.getFetchAcc())
//                                .fetchPassw(upgradeData.getFetchPassw())
                                .pgList(upgradeData.getPcItemList().stream()
                                    .map(x -> new PgItemInfo().setPgType(x.getBinName())
                                        .setUrl(x.getPath()))
                                    .collect(Collectors.toList()));
                            EssUpgradeReq.REQ req = builder.build();
                            mqService.publishMessage(gwInfo, false, JsonUtils.toJsonString(req));

                            // 更新记录信息
                            essRwDs.updateEss(new EssPo().setDno(param.getEssDno())
                                .setExpectUpgradeLogId(upgradeData.getUpgradeLogId()));

                            // FIXME: 下发超时监听
                            IotGwDownCmd downCmd = new IotGwDownCmd();
                            downCmd.setTtl(13 * 60)
                                .setMsg(JsonUtils.toJsonString(req.getData()))
                                .setCmd(IotGwCmdType2.ESS_D_UPGRADE_OP)
                                .setGwno(dtu.getGwno())
                                .setSeq(dtu.getSerialNo())
                                .setVer(gwInfo.getVer());

                            this.dcEventPublish.publishIotGwDownCmd(downCmd);
                        } else {
                            log.error("网关版本无效: {}，无法下发获取ESS配置指令。", ess.getGwno());
                        }
                    }
                }

                return Mono.just(param);
            });
    }

    public Mono<ObjectResponse<UpgradeLogVo>> getDeviceUpgradeInfo(String dno) {
        final EssPo ess = essRoDs.getByDno(dno);
        IotAssert.isNotNull(ess, "ess.dno.invalid");

        if (null == ess.getExpectUpgradeLogId()) {
            return Mono.just(RestUtils.buildObjectResponse(null));
        }

        UpgradeLogPo upgradeLogPo = upgradeLogRoDs.getById(ess.getExpectUpgradeLogId());
        UpgradeLogVo result = new UpgradeLogVo();
        if (null != upgradeLogPo) {
            BeanUtils.copyProperties(upgradeLogPo, result);
        }
        return Mono.just(RestUtils.buildObjectResponse(result));
    }

    public Mono<BaseResponse> deviceSoftStart(String dno) {
        final EssPo ess = essRoDs.getByDno(dno);
        IotAssert.isNotNull(ess, "ess.dno.invalid");

        if (List.of(EquipStatus.UNKNOWN, EquipStatus.OFFLINE, EquipStatus.OFF)
            .contains(ess.getStatus())) {
            log.warn("设备不在线,请确认设备状态是否有效");
            throw new DcArgumentException("设备不在线，请确认设备状态是否有效");
        }

        EssDtuPo dtu = essDtuRoDs.getByEssDno(ess.getDno());
        if (dtu != null) {
            GwInfoDto gwInfo = gwInfoRoDs.getByGwno(ess.getGwno());
            if (gwInfo != null) {
                if (gwInfo.getVer() == IotConstants.IOT_GW_VER_1) {
                    throw new DcServiceException("废弃的逻辑", Level.ERROR);
                } else if (gwInfo.getVer() == IotConstants.IOT_GW_VER_2
                    || gwInfo.getVer() == IotConstants.IOT_GW_VER_3) {
                    EssSoftStartReq.builder builder = new EssSoftStartReq.builder(
                        ess.getGwno(), this.sequenceRwService.getNextOutRequestSeq(),
                        ess.getDno());
                    EssSoftStartReq.REQ req = builder.build();
                    mqService.publishMessage(gwInfo, false, JsonUtils.toJsonString(req));
                } else {
                    log.error("网关版本无效: {}，无法下发获取ESS配置指令。", ess.getGwno());
                }
            }
        }

        return Mono.just(RestUtils.success());
    }

    public String userEssReadCfg(String dno, EssDynamicCfgRWParam param) {
        EssPo ess = essRoDs.getByDno(dno);
        IotAssert.isNotNull(ess, "设备编号无效");

        if (List.of(EquipStatus.UNKNOWN, EquipStatus.OFFLINE, EquipStatus.OFF)
            .contains(ess.getStatus())) {
            log.warn("EMS 不在线,请确认设备状态是否有效");
            throw new DcArgumentException("ems.offline");
        }

        EssDtuPo dtu = essDtuRoDs.getByEssDno(dno); // 户储ESS和通讯设备是1:1关系
        IotAssert.isNotNull(dtu, "关联DTU不存在");

        GwInfoPo gwInfo = this.gwInfoRoDs.getByGwno(dtu.getGwno());
        IotAssert.isNotNull(gwInfo, "关联网关不存在");

        String deliverSeq = this.sequenceRwService.getNextOutRequestSeq();
        if (gwInfo.getVer() == IotConstants.IOT_GW_VER_1) {
            throw new DcServiceException("废弃的逻辑", Level.ERROR);
        } else if (gwInfo.getVer() == IotConstants.IOT_GW_VER_2
            || gwInfo.getVer() == IotConstants.IOT_GW_VER_3) {
            GetEssCfgReq.builder builder = new GetEssCfgReq.builder(ess.getGwno(), deliverSeq);
            GetEssCfgReq.REQ req = builder.dno(dno)
                .serialNo(dtu.getSerialNo())
                .category(param.getCategory())
                .tvs(param.getTvs()).build();
            mqService.publishMessage(gwInfo, false, JsonUtils.toJsonString(req));
        } else {
            log.error("网关版本无效: {}，无法下发获取ESS配置指令。", ess.getGwno());
        }

        return deliverSeq;
    }

    public String userEssRefreshCfg(String dno, EssDynamicCfgRWParam param) {
        EssPo ess = essRoDs.getByDno(dno);
        IotAssert.isNotNull(ess, "设备编号无效");

        EssDtuPo dtu = essDtuRoDs.getByEssDno(dno); // 户储ESS和通讯设备是1:1关系
        IotAssert.isNotNull(dtu, "关联DTU不存在");

        GwInfoPo gwInfo = this.gwInfoRoDs.getByGwno(dtu.getGwno());
        IotAssert.isNotNull(gwInfo, "关联网关不存在");

        String deliverSeq = this.sequenceRwService.getNextOutRequestSeq();
        if (gwInfo.getVer() == IotConstants.IOT_GW_VER_1) {
            throw new DcServiceException("废弃的逻辑", Level.ERROR);
        } else if (gwInfo.getVer() == IotConstants.IOT_GW_VER_2
            || gwInfo.getVer() == IotConstants.IOT_GW_VER_3) {
            DeliverUserEssCfgReq.builder builder = new DeliverUserEssCfgReq.builder(
                gwInfo.getGwno(), deliverSeq);
            builder.setDno(ess.getDno())
                .setSerialNo(dtu.getSerialNo())
                .category(param.getCategory())
                .tvs(param.getTvs());
            DeliverUserEssCfgReq.REQ req = builder.build();
            mqService.publishMessage(gwInfo, false, JsonUtils.toJsonString(req));

            // FIXME: 下发超时监控

            // 收录下发记录存放数据库
        } else {
            log.error("网关版本无效: {}，无法下发获取ESS配置指令。", ess.getGwno());
        }
        return deliverSeq;
    }
}
