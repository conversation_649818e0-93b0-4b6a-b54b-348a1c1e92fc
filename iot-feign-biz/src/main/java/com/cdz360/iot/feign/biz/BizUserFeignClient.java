package com.cdz360.iot.feign.biz;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.iot.model.comm.vo.CommercialManageVo;
import com.cdz360.iot.model.sys.vo.Dict;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import reactivefeign.spring.config.ReactiveFeignClient;
import reactor.core.publisher.Mono;

@ReactiveFeignClient(name = DcConstants.KEY_FEIGN_DC_BIZ_USER,
    fallbackFactory = BizUserFeignHystrix.class)
public interface BizUserFeignClient {


    @GetMapping(value = "/api/sysDict/findDictDataByType")
    Mono<ListResponse<Dict>> findDictDataByTypeV2(@RequestParam(value = "type") String type);

}
