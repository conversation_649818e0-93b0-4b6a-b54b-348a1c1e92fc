package com.cdz360.iot.model.ess.dto;

import com.cdz360.base.model.iot.type.EssEquipType;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class EssRtReq<T> {
    @Schema(description = "EMS设备的SN")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String sn;

    @Schema(description = "云端给ESS分配的唯一编号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String dno;

    @Schema(description = "设备ID(ess内唯一)")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long equipId;

    @Schema(description = "设备类型ID")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer equipTypeId;

    @Schema(description = "设备类型")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private EssEquipType equipType;

    @Schema(description = "具体设备运行时参数")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private T rtData;
}
