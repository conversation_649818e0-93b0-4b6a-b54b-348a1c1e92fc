package com.cdz360.iot.model.ess.param;


import com.cdz360.iot.model.ess.type.EssBootMode;
import com.cdz360.iot.model.ess.type.EssSwitchCommand;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;


@Data

@Accessors(chain = true)

@Schema(description = "储能开关机设置")

public class EssCfgParam {

    @Schema(description = "开机方式: AUTO(自动); MANUAL(手动)")
    private EssBootMode bootMode;

    @Schema(description = "开关机命令: ON(开); OFF(关)")
    private EssSwitchCommand switchCommand;
}

