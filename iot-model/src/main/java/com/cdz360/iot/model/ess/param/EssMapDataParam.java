package com.cdz360.iot.model.ess.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.ExternalDocumentation;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "户用储能设备数据汇总查询参数")
@Data
@Accessors(chain = true)
public class EssMapDataParam {

    @Schema(description = "国家地区代码(Alpha-3 code)",
        externalDocs = @ExternalDocumentation(
            description = "iso-3166 Country Codes",
            url = "https://www.iso.org/obp/ui/#search"))
    private String countryCode;

    @Schema(description = "场站组ID列表")
    @JsonInclude(Include.NON_NULL)
    private List<String> gids;
}
