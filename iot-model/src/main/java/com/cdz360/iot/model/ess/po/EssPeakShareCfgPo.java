package com.cdz360.iot.model.ess.po;


import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.List;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;


@Data

@Accessors(chain = true)

@Schema(description = "储能ESS充放电时段参数配置")

public class EssPeakShareCfgPo {



	@Schema(description = "模板id(t_dev_cfg.id)")

	@NotNull(message = "cfgId 不能为 null")

	private Long cfgId;




	@Schema(description = "削峰填谷使能")

	@NotNull(message = "peakFillEnable 不能为 null")

	private Boolean peakFillEnable;



	@Schema(description = "峰时间段, 单位: 分钟: [{start: 100, end: 600}]")

	private List<RangeTime> peakTime;



	@Schema(description = "峰时段功率, 单位: kW")

	private BigDecimal peakPower;



	@Schema(description = "谷时间段, 单位: 分钟: [{start: 100, end: 600}]")

	private List<RangeTime> valleyTime;



	@Schema(description = "谷时段功率, 单位: kW")

	private BigDecimal valleyPower;



	@Schema(description = "平滑输出使能")

	@NotNull(message = "smoothOutputEnable 不能为 null")

	private Boolean smoothOutputEnable;



	@Schema(description = "监测周期")

	private Integer monitoringPeriod;



	@Schema(description = "变化幅值")

	private Integer amplitude;



	@Schema(description = "目标额定功率, 单位: kW")

	private BigDecimal targetPowerRating;





}

