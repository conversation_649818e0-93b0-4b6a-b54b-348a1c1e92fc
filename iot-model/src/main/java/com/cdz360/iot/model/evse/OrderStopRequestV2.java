package com.cdz360.iot.model.evse;

//import com.cdz360.iot.model.order.type.OrderStartType;

import com.cdz360.base.model.charge.type.OrderStartType;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class OrderStopRequestV2 extends OrderReportRequestV2 {

    @Schema(description = "唯一的订单号")
    private String orderNo;
    //    private GwOrderEvent event;
    @Schema(description = "桩编号")
    private String evseNo;


    @Schema(description = "充电枪编号")
    private String plugNo;

    @Schema(description = "充电枪ID")
    private Integer plugId;

    @Schema(description = "充电完成原因. 0 为正常完成, 其他都为非正常完成")
    private long stopCode;//
    @Schema(description = "消费电量, 单位'kwh'")
    private BigDecimal kwh;//
    @Schema(description = "充电开始前电表读数, 单位'KWH'")
    private BigDecimal startMeter;
    @Schema(description = "充电完成后电表读数, 单位'KWH'")
    private BigDecimal stopMeter;
    @Schema(description = "当前累计电费金额, 单位'元'")
    private BigDecimal elecFee;//
    @Schema(description = "当前累计服务费金额, 单位'元'")
    private BigDecimal servFee;//
    @JsonDeserialize(using = CustomDateDeserializer.class)
    @JsonSerialize(using = CustomDateSerializer.class)
    @Schema(description = "开始充电时间, unix时间戳")
    private Date startTime;//
    @JsonDeserialize(using = CustomDateDeserializer.class)
    @JsonSerialize(using = CustomDateSerializer.class)
    @Schema(description = "停止充电时间, unix时间戳")
    private Date stopTime;//
    @Schema(description = "开启方式:0x01: 紧急充电卡订单 0x02: 无卡启动 0x11: 在线卡启动 0x12: VIN码启动 0x21: 管理后台 0x22: 批量启动 0x31: 微信公众号 0x32: 微信小程序 0x33: iOS APP 0x34: 安卓 APP")
    private OrderStartType startType;
    @Schema(description = "账号")
    private String accountNo;
    @Schema(description = "车架号")
    private String vin;//
    @Schema(description = "充电开始时的soc")
    private Integer startSoc;//
    @Schema(description = "充电结束时的soc")
    private Integer soc;//
    @Schema(description = "电价模板编号")
    private Integer priceCode;//
    @Schema(description = "账单详情")
    private List<OrderDetailV2> detail;



/*
    //@JsonFormat(shape = JsonFormat.Shape.NUMBER_INT, pattern = "s")
    @Schema(description = "订单可用余额, 单位'分'")
    private long balance;//

    public Integer getStartMeter() {
        return startMeter;
    }

    public void setStartMeter(Integer startMeter) {
        this.startMeter = startMeter;
    }

    public Integer getStopMeter() {
        return stopMeter;
    }

    public void setStopMeter(Integer stopMeter) {
        this.stopMeter = stopMeter;
    }

    public long getStopCode() {
        return stopCode;
    }

    public OrderStopRequestV2 setStopCode(long stopCode) {
        this.stopCode = stopCode;
        return this;
    }

    public Integer getKwh() {
        return kwh;
    }

    public OrderStopRequestV2 setKwh(Integer kwh) {
        this.kwh = kwh;
        return this;
    }

    public Integer getElecFee() {
        return elecFee;
    }

    public OrderStopRequestV2 setElecFee(Integer elecFee) {
        this.elecFee = elecFee;
        return this;
    }

    public Integer getServFee() {
        return servFee;
    }

    public OrderStopRequestV2 setServFee(Integer servFee) {
        this.servFee = servFee;
        return this;
    }

    public Date getStartTime() {
        return startTime;
    }

    public OrderStopRequestV2 setStartTime(Date startTime) {
        this.startTime = startTime;
        return this;
    }

    public Date getStopTime() {
        return stopTime;
    }

    public OrderStopRequestV2 setStopTime(Date stopTime) {
        this.stopTime = stopTime;
        return this;
    }

    public String getVin() {
        return vin;
    }

    public OrderStopRequestV2 setVin(String vin) {
        this.vin = vin;
        return this;
    }

    public Integer getSoc() {
        return soc;
    }

    public OrderStopRequestV2 setSoc(Integer soc) {
        this.soc = soc;
        return this;
    }

    public long getBalance() {
        return balance;
    }

    public OrderStopRequestV2 setBalance(long balance) {
        this.balance = balance;
        return this;
    }

    public Integer getPriceCode() {
        return priceCode;
    }

    public OrderStopRequestV2 setPriceCode(Integer priceCode) {
        this.priceCode = priceCode;
        return this;
    }

    public List<OrderDetail> getDetail() {
        return detail;
    }

    public OrderStopRequestV2 setDetail(List<OrderDetail> detail) {
        this.detail = detail;
        return this;
    }

    public String getAccountNo() {
        return accountNo;
    }

    public void setAccountNo(String accountNo) {
        this.accountNo = accountNo;
    }

    public OrderStartType getStartType() {
        return startType;
    }

    public void setStartType(OrderStartType startType) {
        this.startType = startType;
    }
*/
}
