package com.cdz360.iot.model.evse.cfg;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.model.base.BaseObject;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Classname CfgEvseAll
 * @Description
 */
@Data
@EqualsAndHashCode(callSuper = true)
//@ToString(callSuper = true)
public class CfgEvseAllV2 extends BaseObject {

    @Schema(description = "桩编号")
    private String evseNo;//桩编号

    @Schema(description = "桩端长效秘钥版本号")
    private Integer evsePasscodeVer;//桩端长效秘钥版本号

    @CustomWarningDesc(desc = "桩配置(管理员密码)与平台不一致")
    @Schema(description = "桩端管理员登录密码, 数字型, 6~8位. 不传表示不做修改")
    private String adminCodeA;//桩端管理员登录密码, 数字型, 6~8位. 不传表示不做修改

    @CustomWarningDesc(desc = "桩配置(二级管理员密码)与平台不一致")
    @Schema(description = "桩端二级管理员登录密码,数字型, 6~8位. 不传表示不做修改")
    private String adminCodeB;//桩端二级管理员登录密码,数字型, 6~8位. 不传表示不做修改

    @CustomWarningDesc(desc = "桩配置(计费模板)与平台不一致")
    @Schema(description = "计费模板ID")
    private Integer priceCode;//计费模板ID

    @Schema(description = "price;//计费方案")
    private List<ChargeV2> price;//计费方案

    @CustomWarningDesc(desc = "桩配置(是否支持VIN码充电)与平台不一致")
    @Schema(description = "是否支持 VIN 码充电. 不传表示不做修改")
    private Boolean vin;//是否支持 VIN 码充电. 不传表示不做修改

    @CustomWarningDesc(desc = "桩配置(二维码地址)与平台不一致")
    @Schema(description = "桩端显示的二维码 URL. 不传表示不做修改")
    private String qrUrl;//桩端显示的二维码 URL. 不传表示不做修改

    @Schema(description = "whiteCards;//紧急充电卡列表. 不传表示不做修改")
    private List<WhiteCardV2> whiteCards;//紧急充电卡列表. 不传表示不做修改

    @Schema(description = "whiteVinList;//本地VIN. 不传表示不做修改")
    private List<WhiteVin> whiteVinList;//本地VIN. 不传表示不做修改

    @CustomWarningDesc(desc = "桩配置(国标协议)与平台不一致")
    @Schema(description = "BMS协议.")
    private EvseCfgEnum bmsVer;//BMS协议.

    @CustomWarningDesc(desc = "桩配置(是否自动停充)与平台不一致")
    @Schema(description = "是否自动停充.")
    private Boolean autoStop;//是否自动停充.

    @CustomWarningDesc(desc = "桩配置(均/轮充模式)与平台不一致")
    @Schema(description = "均/轮充模式")
    private EvseCfgEnum balanceMode;//均/轮充模式

    @CustomWarningDesc(desc = "桩配置(是否支持合充)与平台不一致")
    @Schema(description = "合充开关")
    private Boolean combination;//合充开关

    @CustomWarningDesc(desc = "桩配置(是否支持辅电手动切换)与平台不一致")
    @Schema(description = "辅电手动切换开关")
    private Boolean heating;//辅电手动切换开关

    @CustomWarningDesc(desc = "桩配置(辅电电压设置)与平台不一致")
    @Schema(description = "辅电电压设置  12V or 24V")
    private Integer heatingVoltage;

    @CustomWarningDesc(desc = "桩配置(是否支持电池反接检测)与平台不一致")
    @Schema(description = "电池反接检测开关")
    private Boolean batteryCheck;//电池反接检测开关

    @CustomWarningDesc(desc = "桩配置(绝缘检测类型)与平台不一致")
    @Schema(description = "绝缘检测类型")
    private EvseCfgEnum isolation;//绝缘检测类型

    @Schema(description = "手动充电开关")
    private Boolean manualMode;//手动充电开关

    @CustomWarningDesc(desc = "桩配置(是否支持充电记录查询)与平台不一致")
    @Schema(description = "是否支持充电记录查询. 不传表示不做修改")
    private Boolean queryChargeRecord;//是否支持充电记录查询. 不传表示不做修改

    @CustomWarningDesc(desc = "桩配置(是否支持主动安全检测)与平台不一致")
    @Schema(description = "主动安全检测开关")
    private Boolean securityCheck;

    @CustomWarningDesc(desc = "桩配置(是否支持不拔枪充电)与平台不一致")
    @Schema(description = "不拔枪充电开关(二次充电)")
    private Boolean constantCharge;

    @CustomWarningDesc(desc = "桩配置(是否支持插枪获取VIN)与平台不一致")
    @Schema(description = "插枪获取VIN开关")
    private Boolean vinDiscover;

    @CustomWarningDesc(desc = "桩配置(是否可见订单信息隐私)与平台不一致")
    @Schema(description = "订单信息隐私设置开关（null不做更改 true开启 false关闭）")
    private Boolean orderPrivacySetting;

    /**
     * null 不做更改
     * 1 鉴权账号，刷卡时为卡号，VIN时为VIN，平台启动为手机号
     * 2 车牌号（没有车牌号时默认显示类型为0x01的内容）
     */
    @CustomWarningDesc(desc = "桩配置(订单账号显示类型)与平台不一致")
    @Schema(description = "订单账号显示类型")
    private Integer accountDisplayType;

    @CustomWarningDesc(desc = "桩配置(是否支持扫码充电)与平台不一致")
    @Schema(description = "是否支持扫码充电. 不传表示不做修改")
    private Boolean qrCharge;//是否支持扫码充电. 不传表示不做修改

    @CustomWarningDesc(desc = "桩配置(是否支持刷卡充电)与平台不一致")
    @Schema(description = "是否支持刷卡充电. 不传表示不做修改")
    private Boolean cardCharge;//是否支持刷卡充电. 不传表示不做修改

    @CustomWarningDesc(desc = "桩配置(是否支持无卡充电)与平台不一致")
    @Schema(description = "是否支持无卡充电. 不传表示不做修改")
    private Boolean noCardCharge;//是否支持无卡充电. 不传表示不做修改

    @CustomWarningDesc(desc = "桩配置(是否支持定时充电)与平台不一致")
    @Schema(description = "是否支持定时充电. 不传表示不做修改")
    private Boolean timedCharge;//是否支持定时充电. 不传表示不做修改

    @CustomWarningDesc(desc = "桩配置(白天音量)与平台不一致")
    @Schema(description = "白天音量. 不传表示不做修改")
    private Integer dayVolume;//白天音量. 不传表示不做修改

    @CustomWarningDesc(desc = "桩配置(夜间音量)与平台不一致")
    @Schema(description = "夜间音量. 不传表示不做修改")
    private Integer nightVolume;//夜间音量. 不传表示不做修改

    @CustomWarningDesc(desc = "桩配置(是否支持固定电量、固定金额、固定时长充电)与平台不一致")
    @Schema(description = "充电停止方式, 不传表示不做变更")
    private ChargeStopMode stopMode;//充电停止方式, 不传表示不做变更


    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
