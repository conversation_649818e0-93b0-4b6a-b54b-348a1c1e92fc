package com.cdz360.iot.model.evse.cfg;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.model.base.BaseObject;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class CfgEvseV2 extends BaseObject {

    @Schema(description = "桩端长效秘钥版本号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Integer evsePasscodeVer;

    @Schema(description = "桩端长效秘钥")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String evsePasscode;

    @Schema(description = "桩端一级管理员登录密码, 数字型, 6~8位. 不传表示不做修改")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String adminCodeA;

    @Schema(description = "桩端二级管理员登录密码, 数字型, 6~8位. 不传表示不做修改")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String adminCodeB;

    @Schema(description = "是否支持 VIN 码充电. 不传表示不做修改")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Boolean vin;

    @Schema(description = "是否支持充电记录查询. 不传表示不做修改")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Boolean queryChargeRecord;

    @Schema(description = "是否支持扫码充电. 不传表示不做修改")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Boolean qrCharge;

    @Schema(description = "是否支持刷卡充电. 不传表示不做修改")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Boolean cardCharge;

    @Schema(description = "是否支持无卡充电. 不传表示不做修改")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Boolean noCardCharge;

    @Schema(description = "是否支持定时充电. 不传表示不做修改")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Boolean timedCharge;

    @Schema(description = "白天音量. 不传表示不做修改")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Integer dayVolume;

    @Schema(description = "夜间音量. 不传表示不做修改")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Integer nightVolume;

    @Schema(description = "桩端显示的二维码 URL. 不传表示不做修改")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String qrUrl;

    @Schema(description = "国际协议")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String bmsVer;

    @Schema(description = "辅电电压设置")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer heatingVoltage;

    @Schema(description = "是否支持辅电手动切换")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean heating;

    @Schema(description = "均/轮充设置 0均充 1轮充")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer avgOrTurnCharge;

    @Schema(description = "是否支持电池反接检测")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean batteryCheck;

    @Schema(description = "是否支持主动安全检测")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean securityCheck;

    @Schema(description = "是否支持合充")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean combination;

    // 不拔枪充电开关(二次充电)
    private Boolean constantCharge;

    // 插枪获取VIN开关
    private Boolean vinDiscover;

    // 订单信息隐私设置开关（null不做更改 true开启 false关闭）
    private Boolean orderPrivacySetting;

    /**
     * 订单账号显示类型
     * null 不做更改
     * 1 鉴权账号，刷卡时为卡号，VIN时为VIN，平台启动为手机号
     * 2 车牌号（没有车牌号时默认显示类型为0x01的内容）
     */
    private Integer accountDisplayType;

    @Schema(description = "充电停止方式, 不传表示不做变更")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private ChargeStopMode stopMode;

    @Schema(description = "紧急充电卡列表. 不传表示不做修改")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<WhiteCardV2> whiteCards;

    @Schema(description = "桩本地vin鉴权. 不传表示不做修改")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<WhiteVin> whiteVinList;

    @Schema(description = "计费模板ID. 不传表示不修改价格")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Integer priceCode;

    @Schema(description = "计费方案. 不传表示不做修改")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<ChargeV2> price;

    @Schema(accessMode = Schema.AccessMode.READ_ONLY)
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
//    @JsonIgnore
    private String cfgVer;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }

}
