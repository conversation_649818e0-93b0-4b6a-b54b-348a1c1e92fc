package com.cdz360.iot.model.gw;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 网关上/下行消息基础数据结构
 */
@Data
@Accessors(chain = true)
public class GwMsg {

    @JsonProperty(value = "v")
    @Schema(accessMode = Schema.AccessMode.READ_ONLY)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer ver;

    @JsonProperty(value = "n")
    @Schema(accessMode = Schema.AccessMode.READ_ONLY)
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String gwno;

    @Schema(description = "请求的唯一序列号, 对于当前网关确保唯一, 建议时间戳+数字的形式", example = "201908140823411234")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String seq;

    @Schema(description = "是否为supervisor")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean sup;

    @Schema(description = "网关内网IP")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String lanIp;

    @Schema(description = "网关MAC地址")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String mac;

    //
    @Schema(description = "代码版本")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String gitCommitId;

    //
    @Schema(description = "软件版本")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String swVer;

    //
    @Schema(description = "软件版本号")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer swVerCode;

    //
    @Schema(description = "启动时间,unix时间戳")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long bootTime;
}
