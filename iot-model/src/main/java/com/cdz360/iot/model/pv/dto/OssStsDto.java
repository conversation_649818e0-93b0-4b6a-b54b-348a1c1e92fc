package com.cdz360.iot.model.pv.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "客户端访问OSS信息")
public class OssStsDto {

    @Schema(description = "接入地址", example = "http://oss-cn-hangzhou.aliyuncs.com")
    private String endpoint;

    @Schema(description = "oss key")
    private String accessKeyId;

    @Schema(description = "oss secret")
    private String accessKeySecret;

    @Schema(description = "oss token")
    private String securityToken;

    @Schema(description = "oss expireTime")
    private Long expireTime;

    @Schema(description = "文件上传的bucket")
    private String bucketName;

    @Schema(description = "文件URL的公共部分", example = "https://fourth-platform.oss-cn-shanghai.aliyuncs.com/")
    private String baseUrl;
}
