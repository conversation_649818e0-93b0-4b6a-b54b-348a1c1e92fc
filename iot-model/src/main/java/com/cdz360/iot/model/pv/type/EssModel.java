package com.cdz360.iot.model.pv.type;

import lombok.Getter;

@Getter
public enum EssModel {

    ALPHA_EMS2_5(0),
    ALPHA_EMS3_0(1),
    HT_EMS1_0(2),
    UNKNOWN(99),
    ;

    private final int code;

    EssModel(int code) {
        this.code = code;
    }


    public static EssModel codeOf(Object codeIn) {
        if (codeIn == null) {
            return EssModel.UNKNOWN;
        }
        int code = 0;
        if (codeIn instanceof EssModel) {
            return (EssModel) codeIn;
        } else if (codeIn instanceof Integer) {
            code = ((Integer) codeIn).intValue();
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }
        for (EssModel model : values()) {
            if (model.code == code) {
                return model;
            }
        }
        return EssModel.UNKNOWN;
    }

}
