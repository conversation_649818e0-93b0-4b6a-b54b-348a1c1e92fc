package com.cdz360.iot.task.biz;

import com.cdz360.iot.task.IotTaskTestBase;
import com.cdz360.iot.task.task.IotDeviceMonitorTask;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

public class IotDeviceMonitorTaskTest extends IotTaskTestBase {
    private final Logger logger = LoggerFactory.getLogger(IotDeviceMonitorTaskTest.class);
    @Autowired
    IotDeviceMonitorTask iotDeviceMonitorTask;

//    @Autowired
//    private GwMonitorService gwMonitorService;

    @Test
    public void monitorGw() {
        logger.info(">>");
//        iotDeviceMonitorTask.monitorGw();
//        iotDeviceMonitorTask.monitorGw();
        logger.info("<<");
    }
}
